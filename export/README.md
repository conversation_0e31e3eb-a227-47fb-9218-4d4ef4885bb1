# Alibaba Data Processing

This directory contains scripts to process Alibaba scraped product data and transform it into the format expected by our app's new schema.

## Files

- **`data.json`** - Raw Alibaba product data (220k+ products)
- **`exported_data_description.md`** - Description of the Alibaba data structure
- **`process_alibaba_data.py`** - Python script to transform the data
- **`process_alibaba_data.ipynb`** - Jupyter notebook with step-by-step processing
- **`getData.ipynb`** - Original script used to export data from Firestore

## Usage

### Option 1: Run the Python Script Directly

```bash
cd export
python process_alibaba_data.py
```

### Option 2: Use the Jupyter Notebook

```bash
cd export
jupyter notebook process_alibaba_data.ipynb
```

Then execute the cells in order.

## What the Script Does

1. **Loads** the raw Alibaba data from `data.json`
2. **Transforms** each product from Alibaba's format to our app's schema:
   - Maps basic fields (name → title, etc.)
   - Processes pricing tiers (offers) with both array and object shapes
   - Handles variants with pricing information
   - Extracts custom services
   - Generates provider data
3. **Extracts** supplier information for creating supplier records
4. **Validates** data against our schema requirements
5. **Generates** multiple output files ready for import

## Output Files

After processing, you'll get:

- **`processed_products.json`** - Products in our app's schema format
- **`suppliers_to_create.json`** - Suppliers that need to be created
- **`processing_report.json`** - Detailed statistics and any issues
- **`import_summary.json`** - High-level summary for import planning

## Schema Features Handled

✅ **Complex Pricing**: Quantity-based tiers, variant pricing, custom services
✅ **Multiple Shapes**: Handles both array and object formats from Alibaba
✅ **Provider Data**: Preserves all information needed for purchasing
✅ **Variants**: Full support for color/size/material variants with pricing
✅ **Images**: Prioritizes and processes product images
✅ **Attributes**: Structured product specifications
✅ **Supplier Extraction**: Automatic supplier identification

## Import Process

1. **Create Suppliers First**: Use `suppliers_to_create.json`
2. **Import Products**: Use `processed_products.json` with your Convex import
3. **Validate**: Check variants, pricing tiers, and custom services
4. **Generate Embeddings**: Run image embedding generation for products with images

## Data Quality

The script handles:
- Price parsing (numbers, ranges, strings)
- Currency normalization
- Image prioritization
- Variant shape detection
- Error handling and logging
- Data validation against schema

## Performance

- Processes 200k+ products efficiently
- Batch processing approach
- Progress tracking and error reporting
- Memory-efficient streaming for large datasets