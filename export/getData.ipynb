{"cells": [{"cell_type": "code", "execution_count": 2, "id": "d580cf53", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Firebase initialized successfully.\n"]}, {"data": {"text/plain": ["<google.cloud.firestore_v1.client.Client at 0x122c4ca40>"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["from firebase_admin import credentials, firestore\n", "import firebase_admin\n", "import time\n", "import pandas as pd\n", "from IPython.display import display, HTML\n", "import matplotlib.pyplot as plt\n", "from tqdm.notebook import tqdm\n", "\n", "# Constants\n", "BATCH_SIZE = 500  # Firestore has a limit of 500 operations per batch\n", "COLLECTION_NAME = 'images_data'\n", "\n", "# Global variables to track if Firebase is initialized\n", "firebase_initialized = False\n", "db = None\n", "\n", "def initialize_firebase(key_file_path=None):\n", "    \"\"\"\n", "    Initialize Firebase with the given service account key file.\n", "\n", "    Args:\n", "        key_file_path (str, optional): Path to the Firebase service account key file.\n", "            If None, tries to use application default credentials.\n", "\n", "    Returns:\n", "        firestore.Client: Firestore client\n", "    \"\"\"\n", "    global firebase_initialized, db\n", "\n", "    if firebase_initialized:\n", "        print(\"Firebase already initialized.\")\n", "        return db\n", "\n", "    try:\n", "        if key_file_path:\n", "            cred = credentials.Certificate(key_file_path)\n", "            firebase_admin.initialize_app(cred)\n", "        else:\n", "            # Try to use application default credentials\n", "            firebase_admin.initialize_app()\n", "\n", "        db = firestore.client()\n", "        firebase_initialized = True\n", "        print(\"✅ Firebase initialized successfully.\")\n", "        return db\n", "    except Exception as e:\n", "        print(f\"❌ Error initializing Firebase: {e}\")\n", "        raise\n", "\n", "initialize_firebase(key_file_path='ServiceAccount.json')"]}, {"cell_type": "code", "execution_count": 9, "id": "a271fc5b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Exporting documents from collection 'alibaba_products'...\n", "Successfully exported 220262 documents to data.json\n", "Total batches: 0\n"]}], "source": ["import json\n", "from google.api_core.retry import Retry\n", "import datetime\n", "\n", "COLLECTION_NAME = 'alibaba_products'\n", "collection_docs = []\n", "\n", "class CustomJSONEncoder(json.JSONEncoder):\n", "    \"\"\"\n", "    A custom JSON encoder to serialize datetime objects to ISO 8601 strings.\n", "    \"\"\"\n", "    def default(self, obj):\n", "        if isinstance(obj, datetime.datetime):\n", "            return obj.isoformat()\n", "        return super(<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self).default(obj)\n", "\n", "def export_collection_to_json(collection_name='alibaba_products', output_file=\"data.json\"):\n", "    \"\"\"\n", "    Exports all documents from a Firestore collection to a JSON file.\n", "\n", "    Args:\n", "        collection_name (str): The name of the Firestore collection to export.\n", "        output_file (str): The path to the output JSON file.\n", "    \"\"\"\n", "    try:\n", "        # Get a reference to the collection\n", "        collection_ref = db.collection(collection_name)\n", "\n", "        # Use .stream() to efficiently iterate over documents\n", "        docs = collection_ref.stream(retry=Retry())\n", "\n", "        all_docs = {}\n", "        print(f\"Exporting documents from collection '{collection_name}'...\")\n", "\n", "        for doc in docs:\n", "            all_docs[doc.id] = doc.to_dict()\n", "\n", "        with open(output_file, 'w') as f:\n", "            json.dump(all_docs, f, indent=4, cls=CustomJSONEncoder)\n", "\n", "        print(f\"Successfully exported {len(all_docs)} documents to {output_file}\")\n", "\n", "    except Exception as e:\n", "        print(f\"An error occurred: {e}\")\n", "    finally:\n", "        # It's good practice to delete the app instance when you're done\n", "        if firebase_admin._apps:\n", "            firebase_admin.delete_app(firebase_admin.get_app())\n", "\n", "# Run the function to process all products\n", "export_collection_to_json()\n", "print(f\"Total batches: {len(collection_docs)}\")"]}, {"cell_type": "code", "execution_count": 3, "id": "4740928c", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "df = pd.read_json('./data.json')"]}, {"cell_type": "code", "execution_count": 6, "id": "7545cef0", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>000S1to5F0oHquWY7bS5</th>\n", "      <th>000elqC30HVFsYqVzG1v</th>\n", "      <th>001RrAIrIQXyXp5AuzyX</th>\n", "      <th>001y5qS7QPXrwIj0vPc7</th>\n", "      <th>003W15pwxtmqCoDWaF8N</th>\n", "      <th>0041vqngOWhLdsTcMUDj</th>\n", "      <th>006fcVNIzcJti8MPozC8</th>\n", "      <th>007cewcS1NIOGzSoBNYA</th>\n", "      <th>00A9fWXcD4kx9zfFIQZk</th>\n", "      <th>00CTJTrqTfXRNNTfQyT2</th>\n", "      <th>...</th>\n", "      <th>zzmLzXK9LP9jG7nCFAzn</th>\n", "      <th>zzmVr6b6e2r0oME9qn85</th>\n", "      <th>zzoI1nGVuisSS9aGDaTl</th>\n", "      <th>zzslsx6LmHTcyjCxpkn0</th>\n", "      <th>zzuEKsQKiHCQYtM1iDpo</th>\n", "      <th>zzwG7bNxCIc0fFRSTYDH</th>\n", "      <th>zzxXByoHqEpKraAbM52H</th>\n", "      <th>zzxxhlHSr3TuSaTb6iGV</th>\n", "      <th>zzyL42O4ZvYQxSWJonHm</th>\n", "      <th>zzzuAwLvuYOGm38R40Ut</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>versionImages</th>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN01m3bE...</td>\n", "      <td>[https://img.alicdn.com/imgextra/i2/**********...</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/2016/643/8...</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN01ptTH...</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN01zedz...</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN01Kf6s...</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN01nazY...</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN01YLAW...</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN01ewH3...</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN01H16R...</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>claimedBy</th>\n", "      <td>NUlVmw1uP8fjAaxnpJUeSnwOxiH2</td>\n", "      <td>NUlVmw1uP8fjAaxnpJUeSnwOxiH2</td>\n", "      <td>NUlVmw1uP8fjAaxnpJUeSnwOxiH2</td>\n", "      <td>NUlVmw1uP8fjAaxnpJUeSnwOxiH2</td>\n", "      <td>NUlVmw1uP8fjAaxnpJUeSnwOxiH2</td>\n", "      <td>NUlVmw1uP8fjAaxnpJUeSnwOxiH2</td>\n", "      <td>NUlVmw1uP8fjAaxnpJUeSnwOxiH2</td>\n", "      <td>NUlVmw1uP8fjAaxnpJUeSnwOxiH2</td>\n", "      <td>NUlVmw1uP8fjAaxnpJUeSnwOxiH2</td>\n", "      <td>NUlVmw1uP8fjAaxnpJUeSnwOxiH2</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>lastProcessed</th>\n", "      <td>2025-01-07T10:18:54.852000+00:00</td>\n", "      <td>2025-01-09T11:38:57.537000+00:00</td>\n", "      <td>2025-01-09T09:28:55.478000+00:00</td>\n", "      <td>2025-01-07T15:34:37.600000+00:00</td>\n", "      <td>2025-01-09T10:27:47.657000+00:00</td>\n", "      <td>2025-01-09T09:39:25.249000+00:00</td>\n", "      <td>2025-01-09T10:24:49.214000+00:00</td>\n", "      <td>2025-01-08T12:43:27.209000+00:00</td>\n", "      <td>2025-01-07T14:34:34.677000+00:00</td>\n", "      <td>2025-01-09T08:42:57.938000+00:00</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>productImagePreviews</th>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN01Nfl8...</td>\n", "      <td>[https://img.alicdn.com/imgextra/i2/**********...</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/2016/718/2...</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN01ptTH...</td>\n", "      <td>[https://img.alicdn.com/imgextra/i1/**********...</td>\n", "      <td>[https://img.alicdn.com/imgextra/i4/O1CN01SSxw...</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN01CBEz...</td>\n", "      <td>[https://img.alicdn.com/imgextra/i3/**********...</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN01mstj...</td>\n", "      <td>[https://img.alicdn.com/imgextra/i4/O1CN01SSxw...</td>\n", "      <td>...</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN01YClV...</td>\n", "      <td>[https://img.alicdn.com/imgextra/i4/O1CN01SSxw...</td>\n", "      <td>[https://img.alicdn.com/imgextra/i4/O1CN01SSxw...</td>\n", "      <td>[https://img.alicdn.com/imgextra/i1/**********...</td>\n", "      <td>[https://img.alicdn.com/imgextra/i4/O1CN01SSxw...</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN01jyjs...</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN01uvum...</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN01wvAe...</td>\n", "      <td>[https://img.alicdn.com/imgextra/i4/O1CN01SSxw...</td>\n", "      <td>[https://img.alicdn.com/imgextra/i4/O1CN01SSxw...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>processedImageDescriptions</th>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>processingStatus</th>\n", "      <td>processed</td>\n", "      <td>processed</td>\n", "      <td>processed</td>\n", "      <td>processed</td>\n", "      <td>processed</td>\n", "      <td>processed</td>\n", "      <td>processed</td>\n", "      <td>processed</td>\n", "      <td>processed</td>\n", "      <td>processed</td>\n", "      <td>...</td>\n", "      <td>unprocessed</td>\n", "      <td>unprocessed</td>\n", "      <td>unprocessed</td>\n", "      <td>unprocessed</td>\n", "      <td>unprocessed</td>\n", "      <td>unprocessed</td>\n", "      <td>unprocessed</td>\n", "      <td>unprocessed</td>\n", "      <td>unprocessed</td>\n", "      <td>unprocessed</td>\n", "    </tr>\n", "    <tr>\n", "      <th>customServices</th>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>[{'serviceName': '加图加字', 'currency': '¥', 'min...</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>...</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>name</th>\n", "      <td>蝴蝶结香槟杯ins风情侣玻璃高脚红酒杯网红粉色蝴蝶玻璃酒杯批发</td>\n", "      <td>成人用品无人售货机选址装修货源全套服务免费加盟无人外卖店</td>\n", "      <td>塑包铝筒灯6寸塑包铝筒灯外壳 LED 筒灯外壳套件厂家直销</td>\n", "      <td>2023春季新款抹胸缩褶开叉缎面礼服裙欧美女装性感修身包臀连衣裙</td>\n", "      <td>双面仿真丝眼罩睡眠航空睡觉遮光眼罩桑蚕丝热敷冰敷冰丝眼罩</td>\n", "      <td>韩版可爱小学生书包 呆萌小狗犀牛熊猫双肩背包 School Bag 男童</td>\n", "      <td>挖机改装螺旋钻机 房屋地基打桩机 拧地钉桩机 挖改螺旋打桩机</td>\n", "      <td>轻奢简约C形山茶花蚊香盘耳环时尚个性法式珍珠吊坠无洞耳夹耳饰</td>\n", "      <td>创可达2024新款圣诞树挂饰3D圣诞老人雪人麋鹿圣诞袜圣诞家居装饰</td>\n", "      <td>跨境健身包新款韩版休闲斜挎包街头简约单肩通勤包帆布水桶包女</td>\n", "      <td>...</td>\n", "      <td>5069-L380ERMS3 控制器 实现特定的控制功能</td>\n", "      <td>厂家代发招5G随身wifi代理自插sim卡无线路由器6000mah四网通MiFi</td>\n", "      <td>inbrixx881101-03泰迪治愈咖啡店烘焙屋兼容乐高积木日式街景礼物</td>\n", "      <td>工装裤子男春秋季新款潮牌宽松直筒裤潮流百搭青少年休闲阔腿长裤</td>\n", "      <td>Casual PU Leather Business Shoulder Bag Crossb...</td>\n", "      <td>钢绞线锚索测力计 使用效率高 山东厂家直销 数显锚杆 锚索测力计</td>\n", "      <td>黑色铁艺加粗三层锅盖架台面砧板菜板沥水置物架家用免打孔收纳架</td>\n", "      <td>韩国跨境妈咪包大容量大号超轻妈妈待产包百搭儿童旅行手提化妆包</td>\n", "      <td>儿童过家玩具娃娃公仔玩偶女孩公主别墅拼装灯光声音模型城堡礼物</td>\n", "      <td>时尚网红同款无框太阳镜2020年新款四方潮墨镜女ins圆脸街拍眼镜</td>\n", "    </tr>\n", "    <tr>\n", "      <th>processingHistory</th>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>attributes</th>\n", "      <td>{'是否进口': '否', '版权': '无', '全国工业生产许可证编号': '详询', ...</td>\n", "      <td>{'是否跨境出口专供货源': '否', '品牌': '双凯', '商品类型': '智能售货机...</td>\n", "      <td>{'是否进口': '否', '表面处理': '哑光光', '加工工艺': '塑包铝', '品...</td>\n", "      <td>NaN</td>\n", "      <td>{'是否进口': '否', '风格': '简约时尚', '功能': '防护遮光', '是否跨...</td>\n", "      <td>NaN</td>\n", "      <td>{'回转速度': '800', '回转角度': '360', '桩工机械类型': '旋挖钻机...</td>\n", "      <td>{'风格': '时尚OL', '款式类别': '蚊香盘耳夹', '流行元素': '山茶花',...</td>\n", "      <td>NaN</td>\n", "      <td>{'上市年份季节': '2024年春季', '风格': '跨境风潮', '里料质地': '涤...</td>\n", "      <td>...</td>\n", "      <td>{'工作电压': '25', '型号': '5069-L380ERMS3', '安装型式':...</td>\n", "      <td>{'产品尺寸': '136*72*16mm', '协议': '802.11ac', '货号'...</td>\n", "      <td>NaN</td>\n", "      <td>{'风格': '简约', '主面料成分': '涤纶/涤纶（聚酯纤维）', '裤型': '宽松...</td>\n", "      <td>NaN</td>\n", "      <td>{'是否进口': '否', '测量精度': '99', '订货号': '354365', '...</td>\n", "      <td>NaN</td>\n", "      <td>{'上市年份季节': '2023年冬季', '功能': '透气,耐磨,减负', '有可授权的...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>productImageDescriptions</th>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN01M4Fm...</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN01fyJE...</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/2016/423/5...</td>\n", "      <td>[]</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN01gbDx...</td>\n", "      <td>[https://cbu01.alicdn.com/cms/upload/other/laz...</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN01asgd...</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN01jlDv...</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/2019/550/4...</td>\n", "      <td>[https://cbu01.alicdn.com/cms/upload/other/laz...</td>\n", "      <td>...</td>\n", "      <td>[https://cbu01.alicdn.com/cms/upload/other/laz...</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN01orcG...</td>\n", "      <td>[https://cbu01.alicdn.com/cms/upload/other/laz...</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN01P6tM...</td>\n", "      <td>[https://cbu01.alicdn.com/cms/upload/other/laz...</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN01e3pg...</td>\n", "      <td>[]</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN013tON...</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN01j6JN...</td>\n", "      <td>[https://cbu01.alicdn.com/cms/upload/other/laz...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>serviceTranslations</th>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>created</th>\n", "      <td>2024-11-18T16:02:52.204000+00:00</td>\n", "      <td>2024-12-03T10:02:40.066000+00:00</td>\n", "      <td>2024-11-29T14:06:17.369000+00:00</td>\n", "      <td>2024-09-12T12:22:30.058000+00:00</td>\n", "      <td>2024-12-10T16:28:58.698000+00:00</td>\n", "      <td>2024-08-26T10:44:21.539000+00:00</td>\n", "      <td>2024-12-09T08:37:43.430000+00:00</td>\n", "      <td>2024-11-01T08:51:02.878000+00:00</td>\n", "      <td>2024-08-18T21:31:00.587000+00:00</td>\n", "      <td>2024-12-06T16:47:38.411000+00:00</td>\n", "      <td>...</td>\n", "      <td>2024-11-21T11:57:12.519000+00:00</td>\n", "      <td>2024-11-24T14:55:57.761000+00:00</td>\n", "      <td>2024-09-12T15:28:15.805000+00:00</td>\n", "      <td>2024-11-05T11:53:35.368000+00:00</td>\n", "      <td>2024-08-27T13:02:47.690000+00:00</td>\n", "      <td>2024-11-05T08:47:00.663000+00:00</td>\n", "      <td>2024-09-16T16:51:19.811000+00:00</td>\n", "      <td>2024-11-20T10:42:49.718000+00:00</td>\n", "      <td>2024-10-07T20:04:28.748000+00:00</td>\n", "      <td>2024-09-23T11:16:35.624000+00:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>batchId</th>\n", "      <td>tdENrmP2qX1VcmEiHR2t</td>\n", "      <td>tdENrmP2qX1VcmEiHR2t</td>\n", "      <td>tdENrmP2qX1VcmEiHR2t</td>\n", "      <td>tdENrmP2qX1VcmEiHR2t</td>\n", "      <td>tdENrmP2qX1VcmEiHR2t</td>\n", "      <td>tdENrmP2qX1VcmEiHR2t</td>\n", "      <td>tdENrmP2qX1VcmEiHR2t</td>\n", "      <td>tdENrmP2qX1VcmEiHR2t</td>\n", "      <td>tdENrmP2qX1VcmEiHR2t</td>\n", "      <td>tdENrmP2qX1VcmEiHR2t</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>weightUnit</th>\n", "      <td></td>\n", "      <td>g</td>\n", "      <td>g</td>\n", "      <td>NaN</td>\n", "      <td>kg</td>\n", "      <td>NaN</td>\n", "      <td>kg</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>g</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>kg</td>\n", "      <td>NaN</td>\n", "      <td>g</td>\n", "      <td>NaN</td>\n", "      <td>kg</td>\n", "      <td>NaN</td>\n", "      <td>g</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>weight</th>\n", "      <td>0</td>\n", "      <td>100000</td>\n", "      <td>240</td>\n", "      <td>NaN</td>\n", "      <td>0.05</td>\n", "      <td>NaN</td>\n", "      <td>0.05</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>170</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>0.8</td>\n", "      <td>NaN</td>\n", "      <td>20</td>\n", "      <td>NaN</td>\n", "      <td>0.5</td>\n", "      <td>NaN</td>\n", "      <td>450</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>translations</th>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>processedImagePreviews</th>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>categories</th>\n", "      <td>[Others]</td>\n", "      <td>[Others]</td>\n", "      <td>[Others]</td>\n", "      <td>[Clothing]</td>\n", "      <td>[Others]</td>\n", "      <td>[Bags]</td>\n", "      <td>[Others]</td>\n", "      <td>[Earrings]</td>\n", "      <td>[Clothing]</td>\n", "      <td>[Bags]</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>variantTranslations</th>\n", "      <td>{'ar': {'types': {'Color': 'لون', 'Size': 'مقا...</td>\n", "      <td>{'ar': {'types': {}, 'names': {}}, 'fr': {'typ...</td>\n", "      <td>{'ar': {'types': {}, 'names': {}}, 'fr': {'typ...</td>\n", "      <td>{'ar': {'types': {'尺码': 'مقاس'}, 'names': {'XL...</td>\n", "      <td>{'ar': {'types': {'Ear Plugs &amp; Packaging': 'سد...</td>\n", "      <td>{'ar': {'types': {'颜色': 'لون'}, 'names': {'c02...</td>\n", "      <td>{'ar': {'types': {}, 'names': {}}, 'fr': {'typ...</td>\n", "      <td>{'ar': {'types': {'颜色': 'لون'}, 'names': {'【15...</td>\n", "      <td>{'ar': {'types': {'规格': 'المواصفات'}, 'names':...</td>\n", "      <td>{'ar': {'types': {'颜色': 'لون'}, 'names': {'米白色...</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>productUrl</th>\n", "      <td>https://detail.1688.com/offer/************.html</td>\n", "      <td>https://detail.1688.com/offer/************.html</td>\n", "      <td>https://detail.1688.com/offer/************.html</td>\n", "      <td>https://detail.1688.com/offer/710881717490.html</td>\n", "      <td>https://detail.1688.com/offer/************.html</td>\n", "      <td>https://detail.1688.com/offer/818410749617.html</td>\n", "      <td>https://detail.1688.com/offer/************.html</td>\n", "      <td>https://detail.1688.com/offer/************.html</td>\n", "      <td>https://detail.1688.com/offer/************.html</td>\n", "      <td>https://detail.1688.com/offer/820546352332.html</td>\n", "      <td>...</td>\n", "      <td>https://detail.1688.com/offer/782354813899.html</td>\n", "      <td>https://detail.1688.com/offer/************.html</td>\n", "      <td>https://detail.1688.com/offer/790437344179.html</td>\n", "      <td>https://detail.1688.com/offer/************.html</td>\n", "      <td>https://detail.1688.com/offer/803334637109.html</td>\n", "      <td>https://detail.1688.com/offer/************.html</td>\n", "      <td>https://detail.1688.com/offer/766761512792.html</td>\n", "      <td>https://detail.1688.com/offer/************.html</td>\n", "      <td>https://detail.1688.com/offer/************.html</td>\n", "      <td>https://detail.1688.com/offer/621448427113.html</td>\n", "    </tr>\n", "    <tr>\n", "      <th>processedAt</th>\n", "      <td>2025-01-07T10:19:19.249000+00:00</td>\n", "      <td>2025-01-09T11:39:07.909000+00:00</td>\n", "      <td>2025-01-09T09:28:59.984000+00:00</td>\n", "      <td>2025-01-07T15:34:45.781000+00:00</td>\n", "      <td>2025-01-09T10:27:53.315000+00:00</td>\n", "      <td>2025-01-09T09:39:34.138000+00:00</td>\n", "      <td>2025-01-09T10:25:07.839000+00:00</td>\n", "      <td>2025-01-08T12:43:54.569000+00:00</td>\n", "      <td>2025-01-07T14:34:46.373000+00:00</td>\n", "      <td>2025-01-09T08:43:00.943000+00:00</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>failedImageDescriptions</th>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>videoUrls</th>\n", "      <td>[https://cloud.video.taobao.com/play/u/2210074...</td>\n", "      <td>[https://cloud.video.taobao.com/play/u/2218565...</td>\n", "      <td>[]</td>\n", "      <td>[https://cloud.video.taobao.com/play/u/2215689...</td>\n", "      <td>[https://cloud.video.taobao.com/play/u/2209222...</td>\n", "      <td>[https://cloud.video.taobao.com/play/u/2217949...</td>\n", "      <td>[https://cloud.video.taobao.com/play/u/2206507...</td>\n", "      <td>[https://cloud.video.taobao.com/play/u/2217288...</td>\n", "      <td>[]</td>\n", "      <td>[https://cloud.video.taobao.com/play/u/2218355...</td>\n", "      <td>...</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>[https://cloud.video.taobao.com/play/u/2217840...</td>\n", "      <td>[https://cloud.video.taobao.com/play/u/2217268...</td>\n", "      <td>[https://cloud.video.taobao.com/play/u/2217185...</td>\n", "      <td>[https://cloud.video.taobao.com/play/u/2218009...</td>\n", "      <td>[]</td>\n", "      <td>[https://cloud.video.taobao.com/play/u/2213239...</td>\n", "      <td>[https://cloud.video.taobao.com/play/u/2208566...</td>\n", "      <td>[https://cloud.video.taobao.com/play/u/3206610...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>processedBy</th>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>failedImagePreviews</th>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>infoProcessed</th>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>True</td>\n", "      <td>...</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>offers</th>\n", "      <td>[{'minQuantity': 50, 'quantityInfo': '50个起批', ...</td>\n", "      <td>[{'minQuantity': 1, 'quantityInfo': '1台起批', 'c...</td>\n", "      <td>[{'minQuantity': 1, 'quantityInfo': '1-999套', ...</td>\n", "      <td>[{'minQuantity': 1, 'quantityInfo': '1-198件', ...</td>\n", "      <td>[{'price': [2.65, 5.85], 'quantityInfo': '价格区间...</td>\n", "      <td>[{'minQuantity': 1, 'quantityInfo': '1-2个', 'c...</td>\n", "      <td>[{'minQuantity': 1, 'quantityInfo': '1部起批', 'c...</td>\n", "      <td>[{'minQuantity': 1, 'quantityInfo': '1对起批', 'c...</td>\n", "      <td>[{'minQuantity': 3, 'quantityInfo': '3只起批', 'c...</td>\n", "      <td>[{'minQuantity': 1, 'quantityInfo': '1-499个', ...</td>\n", "      <td>...</td>\n", "      <td>[{'minQuantity': 1, 'quantityInfo': '1个起批', 'c...</td>\n", "      <td>[{'minQuantity': 1, 'quantityInfo': '1台起批', 'c...</td>\n", "      <td>[{'minQuantity': 1, 'quantityInfo': '1盒起批', 'c...</td>\n", "      <td>[{'minQuantity': 1, 'quantityInfo': '1件起批', 'c...</td>\n", "      <td>[{'minQuantity': 1, 'quantityInfo': '1件起批', 'c...</td>\n", "      <td>[{'minQuantity': 1, 'quantityInfo': '1套起批', 'c...</td>\n", "      <td>[{'minQuantity': 1, 'quantityInfo': '10套成交', '...</td>\n", "      <td>[{'minQuantity': 2, 'quantityInfo': '2个起批', 'c...</td>\n", "      <td>[{'minQuantity': 2, 'quantityInfo': '2盒起批', 'c...</td>\n", "      <td>[{'minQuantity': 1, 'quantityInfo': '1副', 'cur...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mainImages</th>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN01Nfl8...</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN01UvnO...</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/2016/718/2...</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN01Ia5X...</td>\n", "      <td>[https://img.alicdn.com/imgextra/i1/**********...</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN01qEBS...</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN01nazY...</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN01oItp...</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN01mstj...</td>\n", "      <td>[https://img.alicdn.com/imgextra/i3/**********...</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>claimedAt</th>\n", "      <td>2025-01-06T11:27:40.337000+00:00</td>\n", "      <td>2025-01-06T11:27:40.337000+00:00</td>\n", "      <td>2025-01-06T11:27:40.337000+00:00</td>\n", "      <td>2025-01-06T11:27:40.337000+00:00</td>\n", "      <td>2025-01-06T11:27:40.337000+00:00</td>\n", "      <td>2025-01-06T11:27:40.337000+00:00</td>\n", "      <td>2025-01-06T11:27:40.337000+00:00</td>\n", "      <td>2025-01-06T11:27:40.337000+00:00</td>\n", "      <td>2025-01-06T11:27:40.337000+00:00</td>\n", "      <td>2025-01-06T11:27:40.337000+00:00</td>\n", "      <td>...</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "      <td>None</td>\n", "    </tr>\n", "    <tr>\n", "      <th>updated</th>\n", "      <td>2025-01-07T10:19:19.249000+00:00</td>\n", "      <td>2025-01-09T11:39:07.909000+00:00</td>\n", "      <td>2025-01-09T09:28:59.984000+00:00</td>\n", "      <td>2025-01-07T15:34:45.781000+00:00</td>\n", "      <td>2025-01-09T10:27:53.315000+00:00</td>\n", "      <td>2025-01-09T09:39:34.138000+00:00</td>\n", "      <td>2025-01-09T10:25:07.839000+00:00</td>\n", "      <td>2025-01-08T12:43:54.569000+00:00</td>\n", "      <td>2025-01-07T14:34:46.373000+00:00</td>\n", "      <td>2025-01-09T08:43:00.943000+00:00</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2024-11-20T10:42:52.962000+00:00</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>variants</th>\n", "      <td>[{'variantName': '19.5*9.5*8cm', 'variantType'...</td>\n", "      <td>[]</td>\n", "      <td>[{'variantName': 'default', 'variantType': '采购...</td>\n", "      <td>[{'variantName': 'S', 'variantType': '尺码', 'av...</td>\n", "      <td>[{'variantName': '不加耳塞', 'currency': '¥', 'ava...</td>\n", "      <td>[{'variantName': 'c0201a', 'variantType': '颜色'...</td>\n", "      <td>[]</td>\n", "      <td>[{'variantName': '【1569】山茶花珍珠 耳针', 'variantTyp...</td>\n", "      <td>[{'variantName': '2396A＃老人', 'variantType': '规...</td>\n", "      <td>[{'variantName': '黑色', 'variantType': '颜色', 'a...</td>\n", "      <td>...</td>\n", "      <td>[{'variantName': '5069-L380ERMS3', 'variantTyp...</td>\n", "      <td>[{'variantName': '内置电信5G流量卡/扫码充值', 'variantTyp...</td>\n", "      <td>[{'variantName': 'inbrixx881101泰迪治愈咖啡屋', 'vari...</td>\n", "      <td>[{'variantName': 'M 90-105斤', 'variantType': '...</td>\n", "      <td>[{'variantName': 'Bn', 'variantType': '颜色', 'a...</td>\n", "      <td>[{'variantName': '锚索的强度', 'variantType': '规格',...</td>\n", "      <td>[{'variantName': '三层', 'variantType': '层数（规格）'...</td>\n", "      <td>[{'variantName': '小号', 'variantType': '颜色', 'a...</td>\n", "      <td>[{'variantName': '2层5房+灯', 'variantType': '规格'...</td>\n", "      <td>[{'variantName': '金框渐变灰', 'variantType': '镜片颜色...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>variantImages</th>\n", "      <td>{}</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>{}</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>{'颜色-【1569】山茶花珍珠 耳针': ['https://img.alicdn.com...</td>\n", "      <td>{'规格-2396A＃老人': ['https://cbu01.alicdn.com/img...</td>\n", "      <td>{}</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>processedVideos</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>[]</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>offerList</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>processingErrors</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>variants_weight</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>variantsWeight</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>variants_details</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>weightUnit_pants</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>weight_pants</th>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>...</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>41 rows × 220262 columns</p>\n", "</div>"], "text/plain": ["                                                         000S1to5F0oHquWY7bS5  \\\n", "versionImages               [https://cbu01.alicdn.com/img/ibank/O1CN01m3bE...   \n", "claimedBy                                        NUlVmw1uP8fjAaxnpJUeSnwOxiH2   \n", "lastProcessed                                2025-01-07T10:18:54.852000+00:00   \n", "productImagePreviews        [https://cbu01.alicdn.com/img/ibank/O1CN01Nfl8...   \n", "processedImageDescriptions                                               None   \n", "processingStatus                                                    processed   \n", "customServices                                                             []   \n", "name                                          蝴蝶结香槟杯ins风情侣玻璃高脚红酒杯网红粉色蝴蝶玻璃酒杯批发   \n", "processingHistory                                                        None   \n", "attributes                  {'是否进口': '否', '版权': '无', '全国工业生产许可证编号': '详询', ...   \n", "productImageDescriptions    [https://cbu01.alicdn.com/img/ibank/O1CN01M4Fm...   \n", "serviceTranslations                                                      None   \n", "created                                      2024-11-18T16:02:52.204000+00:00   \n", "batchId                                                  tdENrmP2qX1VcmEiHR2t   \n", "weightUnit                                                                      \n", "weight                                                                      0   \n", "translations                                                             None   \n", "processedImagePreviews                                                   None   \n", "categories                                                           [Others]   \n", "variantTranslations         {'ar': {'types': {'Color': 'لون', 'Size': 'مقا...   \n", "productUrl                    https://detail.1688.com/offer/************.html   \n", "processedAt                                  2025-01-07T10:19:19.249000+00:00   \n", "failedImageDescriptions                                                  None   \n", "videoUrls                   [https://cloud.video.taobao.com/play/u/2210074...   \n", "processedBy                                                              None   \n", "failedImagePreviews                                                      None   \n", "infoProcessed                                                            True   \n", "offers                      [{'minQuantity': 50, 'quantityInfo': '50个起批', ...   \n", "mainImages                  [https://cbu01.alicdn.com/img/ibank/O1CN01Nfl8...   \n", "claimedAt                                    2025-01-06T11:27:40.337000+00:00   \n", "updated                                      2025-01-07T10:19:19.249000+00:00   \n", "variants                    [{'variantName': '19.5*9.5*8cm', 'variantType'...   \n", "variantImages                                                              {}   \n", "processedVideos                                                           NaN   \n", "offerList                                                                 NaN   \n", "processingErrors                                                          NaN   \n", "variants_weight                                                           NaN   \n", "variantsWeight                                                            NaN   \n", "variants_details                                                          NaN   \n", "weightUnit_pants                                                          NaN   \n", "weight_pants                                                              NaN   \n", "\n", "                                                         000elqC30HVFsYqVzG1v  \\\n", "versionImages               [https://img.alicdn.com/imgextra/i2/**********...   \n", "claimedBy                                        NUlVmw1uP8fjAaxnpJUeSnwOxiH2   \n", "lastProcessed                                2025-01-09T11:38:57.537000+00:00   \n", "productImagePreviews        [https://img.alicdn.com/imgextra/i2/**********...   \n", "processedImageDescriptions                                               None   \n", "processingStatus                                                    processed   \n", "customServices                                                             []   \n", "name                                             成人用品无人售货机选址装修货源全套服务免费加盟无人外卖店   \n", "processingHistory                                                        None   \n", "attributes                  {'是否跨境出口专供货源': '否', '品牌': '双凯', '商品类型': '智能售货机...   \n", "productImageDescriptions    [https://cbu01.alicdn.com/img/ibank/O1CN01fyJE...   \n", "serviceTranslations                                                      None   \n", "created                                      2024-12-03T10:02:40.066000+00:00   \n", "batchId                                                  tdENrmP2qX1VcmEiHR2t   \n", "weightUnit                                                                  g   \n", "weight                                                                 100000   \n", "translations                                                             None   \n", "processedImagePreviews                                                   None   \n", "categories                                                           [Others]   \n", "variantTranslations         {'ar': {'types': {}, 'names': {}}, 'fr': {'typ...   \n", "productUrl                    https://detail.1688.com/offer/************.html   \n", "processedAt                                  2025-01-09T11:39:07.909000+00:00   \n", "failedImageDescriptions                                                  None   \n", "videoUrls                   [https://cloud.video.taobao.com/play/u/2218565...   \n", "processedBy                                                              None   \n", "failedImagePreviews                                                      None   \n", "infoProcessed                                                            True   \n", "offers                      [{'minQuantity': 1, 'quantityInfo': '1台起批', 'c...   \n", "mainImages                  [https://cbu01.alicdn.com/img/ibank/O1CN01UvnO...   \n", "claimedAt                                    2025-01-06T11:27:40.337000+00:00   \n", "updated                                      2025-01-09T11:39:07.909000+00:00   \n", "variants                                                                   []   \n", "variantImages                                                             NaN   \n", "processedVideos                                                           NaN   \n", "offerList                                                                 NaN   \n", "processingErrors                                                          NaN   \n", "variants_weight                                                           NaN   \n", "variantsWeight                                                            NaN   \n", "variants_details                                                          NaN   \n", "weightUnit_pants                                                          NaN   \n", "weight_pants                                                              NaN   \n", "\n", "                                                         001RrAIrIQXyXp5AuzyX  \\\n", "versionImages               [https://cbu01.alicdn.com/img/ibank/2016/643/8...   \n", "claimedBy                                        NUlVmw1uP8fjAaxnpJUeSnwOxiH2   \n", "lastProcessed                                2025-01-09T09:28:55.478000+00:00   \n", "productImagePreviews        [https://cbu01.alicdn.com/img/ibank/2016/718/2...   \n", "processedImageDescriptions                                               None   \n", "processingStatus                                                    processed   \n", "customServices                                                             []   \n", "name                                            塑包铝筒灯6寸塑包铝筒灯外壳 LED 筒灯外壳套件厂家直销   \n", "processingHistory                                                        None   \n", "attributes                  {'是否进口': '否', '表面处理': '哑光光', '加工工艺': '塑包铝', '品...   \n", "productImageDescriptions    [https://cbu01.alicdn.com/img/ibank/2016/423/5...   \n", "serviceTranslations                                                      None   \n", "created                                      2024-11-29T14:06:17.369000+00:00   \n", "batchId                                                  tdENrmP2qX1VcmEiHR2t   \n", "weightUnit                                                                  g   \n", "weight                                                                    240   \n", "translations                                                             None   \n", "processedImagePreviews                                                   None   \n", "categories                                                           [Others]   \n", "variantTranslations         {'ar': {'types': {}, 'names': {}}, 'fr': {'typ...   \n", "productUrl                    https://detail.1688.com/offer/************.html   \n", "processedAt                                  2025-01-09T09:28:59.984000+00:00   \n", "failedImageDescriptions                                                  None   \n", "videoUrls                                                                  []   \n", "processedBy                                                              None   \n", "failedImagePreviews                                                      None   \n", "infoProcessed                                                            True   \n", "offers                      [{'minQuantity': 1, 'quantityInfo': '1-999套', ...   \n", "mainImages                  [https://cbu01.alicdn.com/img/ibank/2016/718/2...   \n", "claimedAt                                    2025-01-06T11:27:40.337000+00:00   \n", "updated                                      2025-01-09T09:28:59.984000+00:00   \n", "variants                    [{'variantName': 'default', 'variantType': '采购...   \n", "variantImages                                                             NaN   \n", "processedVideos                                                           NaN   \n", "offerList                                                                 NaN   \n", "processingErrors                                                          NaN   \n", "variants_weight                                                           NaN   \n", "variantsWeight                                                            NaN   \n", "variants_details                                                          NaN   \n", "weightUnit_pants                                                          NaN   \n", "weight_pants                                                              NaN   \n", "\n", "                                                         001y5qS7QPXrwIj0vPc7  \\\n", "versionImages               [https://cbu01.alicdn.com/img/ibank/O1CN01ptTH...   \n", "claimedBy                                        NUlVmw1uP8fjAaxnpJUeSnwOxiH2   \n", "lastProcessed                                2025-01-07T15:34:37.600000+00:00   \n", "productImagePreviews        [https://cbu01.alicdn.com/img/ibank/O1CN01ptTH...   \n", "processedImageDescriptions                                               None   \n", "processingStatus                                                    processed   \n", "customServices                                                             []   \n", "name                                         2023春季新款抹胸缩褶开叉缎面礼服裙欧美女装性感修身包臀连衣裙   \n", "processingHistory                                                        None   \n", "attributes                                                                NaN   \n", "productImageDescriptions                                                   []   \n", "serviceTranslations                                                      None   \n", "created                                      2024-09-12T12:22:30.058000+00:00   \n", "batchId                                                  tdENrmP2qX1VcmEiHR2t   \n", "weightUnit                                                                NaN   \n", "weight                                                                    NaN   \n", "translations                                                             None   \n", "processedImagePreviews                                                   None   \n", "categories                                                         [Clothing]   \n", "variantTranslations         {'ar': {'types': {'尺码': 'مقاس'}, 'names': {'XL...   \n", "productUrl                    https://detail.1688.com/offer/710881717490.html   \n", "processedAt                                  2025-01-07T15:34:45.781000+00:00   \n", "failedImageDescriptions                                                  None   \n", "videoUrls                   [https://cloud.video.taobao.com/play/u/2215689...   \n", "processedBy                                                              None   \n", "failedImagePreviews                                                      None   \n", "infoProcessed                                                           False   \n", "offers                      [{'minQuantity': 1, 'quantityInfo': '1-198件', ...   \n", "mainImages                  [https://cbu01.alicdn.com/img/ibank/O1CN01Ia5X...   \n", "claimedAt                                    2025-01-06T11:27:40.337000+00:00   \n", "updated                                      2025-01-07T15:34:45.781000+00:00   \n", "variants                    [{'variantName': 'S', 'variantType': '尺码', 'av...   \n", "variantImages                                                              {}   \n", "processedVideos                                                            []   \n", "offerList                                                                 NaN   \n", "processingErrors                                                          NaN   \n", "variants_weight                                                           NaN   \n", "variantsWeight                                                            NaN   \n", "variants_details                                                          NaN   \n", "weightUnit_pants                                                          NaN   \n", "weight_pants                                                              NaN   \n", "\n", "                                                         003W15pwxtmqCoDWaF8N  \\\n", "versionImages               [https://cbu01.alicdn.com/img/ibank/O1CN01zedz...   \n", "claimedBy                                        NUlVmw1uP8fjAaxnpJUeSnwOxiH2   \n", "lastProcessed                                2025-01-09T10:27:47.657000+00:00   \n", "productImagePreviews        [https://img.alicdn.com/imgextra/i1/**********...   \n", "processedImageDescriptions                                               None   \n", "processingStatus                                                    processed   \n", "customServices              [{'serviceName': '加图加字', 'currency': '¥', 'min...   \n", "name                                             双面仿真丝眼罩睡眠航空睡觉遮光眼罩桑蚕丝热敷冰敷冰丝眼罩   \n", "processingHistory                                                        None   \n", "attributes                  {'是否进口': '否', '风格': '简约时尚', '功能': '防护遮光', '是否跨...   \n", "productImageDescriptions    [https://cbu01.alicdn.com/img/ibank/O1CN01gbDx...   \n", "serviceTranslations                                                      None   \n", "created                                      2024-12-10T16:28:58.698000+00:00   \n", "batchId                                                  tdENrmP2qX1VcmEiHR2t   \n", "weightUnit                                                                 kg   \n", "weight                                                                   0.05   \n", "translations                                                             None   \n", "processedImagePreviews                                                   None   \n", "categories                                                           [Others]   \n", "variantTranslations         {'ar': {'types': {'Ear Plugs & Packaging': 'سد...   \n", "productUrl                    https://detail.1688.com/offer/************.html   \n", "processedAt                                  2025-01-09T10:27:53.315000+00:00   \n", "failedImageDescriptions                                                  None   \n", "videoUrls                   [https://cloud.video.taobao.com/play/u/2209222...   \n", "processedBy                                                              None   \n", "failedImagePreviews                                                      None   \n", "infoProcessed                                                            True   \n", "offers                      [{'price': [2.65, 5.85], 'quantityInfo': '价格区间...   \n", "mainImages                  [https://img.alicdn.com/imgextra/i1/**********...   \n", "claimedAt                                    2025-01-06T11:27:40.337000+00:00   \n", "updated                                      2025-01-09T10:27:53.315000+00:00   \n", "variants                    [{'variantName': '不加耳塞', 'currency': '¥', 'ava...   \n", "variantImages                                                             NaN   \n", "processedVideos                                                           NaN   \n", "offerList                                                                 NaN   \n", "processingErrors                                                          NaN   \n", "variants_weight                                                           NaN   \n", "variantsWeight                                                            NaN   \n", "variants_details                                                          NaN   \n", "weightUnit_pants                                                          NaN   \n", "weight_pants                                                              NaN   \n", "\n", "                                                         0041vqngOWhLdsTcMUDj  \\\n", "versionImages               [https://cbu01.alicdn.com/img/ibank/O1CN01Kf6s...   \n", "claimedBy                                        NUlVmw1uP8fjAaxnpJUeSnwOxiH2   \n", "lastProcessed                                2025-01-09T09:39:25.249000+00:00   \n", "productImagePreviews        [https://img.alicdn.com/imgextra/i4/O1CN01SSxw...   \n", "processedImageDescriptions                                               None   \n", "processingStatus                                                    processed   \n", "customServices                                                             []   \n", "name                                     韩版可爱小学生书包 呆萌小狗犀牛熊猫双肩背包 School Bag 男童   \n", "processingHistory                                                        None   \n", "attributes                                                                NaN   \n", "productImageDescriptions    [https://cbu01.alicdn.com/cms/upload/other/laz...   \n", "serviceTranslations                                                      None   \n", "created                                      2024-08-26T10:44:21.539000+00:00   \n", "batchId                                                  tdENrmP2qX1VcmEiHR2t   \n", "weightUnit                                                                NaN   \n", "weight                                                                    NaN   \n", "translations                                                             None   \n", "processedImagePreviews                                                   None   \n", "categories                                                             [Bags]   \n", "variantTranslations         {'ar': {'types': {'颜色': 'لون'}, 'names': {'c02...   \n", "productUrl                    https://detail.1688.com/offer/818410749617.html   \n", "processedAt                                  2025-01-09T09:39:34.138000+00:00   \n", "failedImageDescriptions                                                  None   \n", "videoUrls                   [https://cloud.video.taobao.com/play/u/2217949...   \n", "processedBy                                                              None   \n", "failedImagePreviews                                                      None   \n", "infoProcessed                                                           False   \n", "offers                      [{'minQuantity': 1, 'quantityInfo': '1-2个', 'c...   \n", "mainImages                  [https://cbu01.alicdn.com/img/ibank/O1CN01qEBS...   \n", "claimedAt                                    2025-01-06T11:27:40.337000+00:00   \n", "updated                                      2025-01-09T09:39:34.138000+00:00   \n", "variants                    [{'variantName': 'c0201a', 'variantType': '颜色'...   \n", "variantImages                                                             NaN   \n", "processedVideos                                                           NaN   \n", "offerList                                                                 NaN   \n", "processingErrors                                                          NaN   \n", "variants_weight                                                           NaN   \n", "variantsWeight                                                            NaN   \n", "variants_details                                                          NaN   \n", "weightUnit_pants                                                          NaN   \n", "weight_pants                                                              NaN   \n", "\n", "                                                         006fcVNIzcJti8MPozC8  \\\n", "versionImages               [https://cbu01.alicdn.com/img/ibank/O1CN01nazY...   \n", "claimedBy                                        NUlVmw1uP8fjAaxnpJUeSnwOxiH2   \n", "lastProcessed                                2025-01-09T10:24:49.214000+00:00   \n", "productImagePreviews        [https://cbu01.alicdn.com/img/ibank/O1CN01CBEz...   \n", "processedImageDescriptions                                               None   \n", "processingStatus                                                    processed   \n", "customServices                                                             []   \n", "name                                           挖机改装螺旋钻机 房屋地基打桩机 拧地钉桩机 挖改螺旋打桩机   \n", "processingHistory                                                        None   \n", "attributes                  {'回转速度': '800', '回转角度': '360', '桩工机械类型': '旋挖钻机...   \n", "productImageDescriptions    [https://cbu01.alicdn.com/img/ibank/O1CN01asgd...   \n", "serviceTranslations                                                      None   \n", "created                                      2024-12-09T08:37:43.430000+00:00   \n", "batchId                                                  tdENrmP2qX1VcmEiHR2t   \n", "weightUnit                                                                 kg   \n", "weight                                                                   0.05   \n", "translations                                                             None   \n", "processedImagePreviews                                                   None   \n", "categories                                                           [Others]   \n", "variantTranslations         {'ar': {'types': {}, 'names': {}}, 'fr': {'typ...   \n", "productUrl                    https://detail.1688.com/offer/************.html   \n", "processedAt                                  2025-01-09T10:25:07.839000+00:00   \n", "failedImageDescriptions                                                  None   \n", "videoUrls                   [https://cloud.video.taobao.com/play/u/2206507...   \n", "processedBy                                                              None   \n", "failedImagePreviews                                                      None   \n", "infoProcessed                                                            True   \n", "offers                      [{'minQuantity': 1, 'quantityInfo': '1部起批', 'c...   \n", "mainImages                  [https://cbu01.alicdn.com/img/ibank/O1CN01nazY...   \n", "claimedAt                                    2025-01-06T11:27:40.337000+00:00   \n", "updated                                      2025-01-09T10:25:07.839000+00:00   \n", "variants                                                                   []   \n", "variantImages                                                             NaN   \n", "processedVideos                                                           NaN   \n", "offerList                                                                 NaN   \n", "processingErrors                                                          NaN   \n", "variants_weight                                                           NaN   \n", "variantsWeight                                                            NaN   \n", "variants_details                                                          NaN   \n", "weightUnit_pants                                                          NaN   \n", "weight_pants                                                              NaN   \n", "\n", "                                                         007cewcS1NIOGzSoBNYA  \\\n", "versionImages               [https://cbu01.alicdn.com/img/ibank/O1CN01YLAW...   \n", "claimedBy                                        NUlVmw1uP8fjAaxnpJUeSnwOxiH2   \n", "lastProcessed                                2025-01-08T12:43:27.209000+00:00   \n", "productImagePreviews        [https://img.alicdn.com/imgextra/i3/**********...   \n", "processedImageDescriptions                                               None   \n", "processingStatus                                                    processed   \n", "customServices                                                             []   \n", "name                                           轻奢简约C形山茶花蚊香盘耳环时尚个性法式珍珠吊坠无洞耳夹耳饰   \n", "processingHistory                                                        None   \n", "attributes                  {'风格': '时尚OL', '款式类别': '蚊香盘耳夹', '流行元素': '山茶花',...   \n", "productImageDescriptions    [https://cbu01.alicdn.com/img/ibank/O1CN01jlDv...   \n", "serviceTranslations                                                      None   \n", "created                                      2024-11-01T08:51:02.878000+00:00   \n", "batchId                                                  tdENrmP2qX1VcmEiHR2t   \n", "weightUnit                                                                NaN   \n", "weight                                                                    NaN   \n", "translations                                                             None   \n", "processedImagePreviews                                                   None   \n", "categories                                                         [Earrings]   \n", "variantTranslations         {'ar': {'types': {'颜色': 'لون'}, 'names': {'【15...   \n", "productUrl                    https://detail.1688.com/offer/************.html   \n", "processedAt                                  2025-01-08T12:43:54.569000+00:00   \n", "failedImageDescriptions                                                  None   \n", "videoUrls                   [https://cloud.video.taobao.com/play/u/2217288...   \n", "processedBy                                                              None   \n", "failedImagePreviews                                                      None   \n", "infoProcessed                                                           False   \n", "offers                      [{'minQuantity': 1, 'quantityInfo': '1对起批', 'c...   \n", "mainImages                  [https://cbu01.alicdn.com/img/ibank/O1CN01oItp...   \n", "claimedAt                                    2025-01-06T11:27:40.337000+00:00   \n", "updated                                      2025-01-08T12:43:54.569000+00:00   \n", "variants                    [{'variantName': '【1569】山茶花珍珠 耳针', 'variantTyp...   \n", "variantImages               {'颜色-【1569】山茶花珍珠 耳针': ['https://img.alicdn.com...   \n", "processedVideos                                                           NaN   \n", "offerList                                                                 NaN   \n", "processingErrors                                                          NaN   \n", "variants_weight                                                           NaN   \n", "variantsWeight                                                            NaN   \n", "variants_details                                                          NaN   \n", "weightUnit_pants                                                          NaN   \n", "weight_pants                                                              NaN   \n", "\n", "                                                         00A9fWXcD4kx9zfFIQZk  \\\n", "versionImages               [https://cbu01.alicdn.com/img/ibank/O1CN01ewH3...   \n", "claimedBy                                        NUlVmw1uP8fjAaxnpJUeSnwOxiH2   \n", "lastProcessed                                2025-01-07T14:34:34.677000+00:00   \n", "productImagePreviews        [https://cbu01.alicdn.com/img/ibank/O1CN01mstj...   \n", "processedImageDescriptions                                               None   \n", "processingStatus                                                    processed   \n", "customServices                                                             []   \n", "name                                        创可达2024新款圣诞树挂饰3D圣诞老人雪人麋鹿圣诞袜圣诞家居装饰   \n", "processingHistory                                                        None   \n", "attributes                                                                NaN   \n", "productImageDescriptions    [https://cbu01.alicdn.com/img/ibank/2019/550/4...   \n", "serviceTranslations                                                      None   \n", "created                                      2024-08-18T21:31:00.587000+00:00   \n", "batchId                                                  tdENrmP2qX1VcmEiHR2t   \n", "weightUnit                                                                NaN   \n", "weight                                                                    NaN   \n", "translations                                                             None   \n", "processedImagePreviews                                                   None   \n", "categories                                                         [Clothing]   \n", "variantTranslations         {'ar': {'types': {'规格': 'المواصفات'}, 'names':...   \n", "productUrl                    https://detail.1688.com/offer/************.html   \n", "processedAt                                  2025-01-07T14:34:46.373000+00:00   \n", "failedImageDescriptions                                                  None   \n", "videoUrls                                                                  []   \n", "processedBy                                                              None   \n", "failedImagePreviews                                                      None   \n", "infoProcessed                                                           False   \n", "offers                      [{'minQuantity': 3, 'quantityInfo': '3只起批', 'c...   \n", "mainImages                  [https://cbu01.alicdn.com/img/ibank/O1CN01mstj...   \n", "claimedAt                                    2025-01-06T11:27:40.337000+00:00   \n", "updated                                      2025-01-07T14:34:46.373000+00:00   \n", "variants                    [{'variantName': '2396A＃老人', 'variantType': '规...   \n", "variantImages               {'规格-2396A＃老人': ['https://cbu01.alicdn.com/img...   \n", "processedVideos                                                           NaN   \n", "offerList                                                                 NaN   \n", "processingErrors                                                          NaN   \n", "variants_weight                                                           NaN   \n", "variantsWeight                                                            NaN   \n", "variants_details                                                          NaN   \n", "weightUnit_pants                                                          NaN   \n", "weight_pants                                                              NaN   \n", "\n", "                                                         00CTJTrqTfXRNNTfQyT2  \\\n", "versionImages               [https://cbu01.alicdn.com/img/ibank/O1CN01H16R...   \n", "claimedBy                                        NUlVmw1uP8fjAaxnpJUeSnwOxiH2   \n", "lastProcessed                                2025-01-09T08:42:57.938000+00:00   \n", "productImagePreviews        [https://img.alicdn.com/imgextra/i4/O1CN01SSxw...   \n", "processedImageDescriptions                                               None   \n", "processingStatus                                                    processed   \n", "customServices                                                             []   \n", "name                                            跨境健身包新款韩版休闲斜挎包街头简约单肩通勤包帆布水桶包女   \n", "processingHistory                                                        None   \n", "attributes                  {'上市年份季节': '2024年春季', '风格': '跨境风潮', '里料质地': '涤...   \n", "productImageDescriptions    [https://cbu01.alicdn.com/cms/upload/other/laz...   \n", "serviceTranslations                                                      None   \n", "created                                      2024-12-06T16:47:38.411000+00:00   \n", "batchId                                                  tdENrmP2qX1VcmEiHR2t   \n", "weightUnit                                                                  g   \n", "weight                                                                    170   \n", "translations                                                             None   \n", "processedImagePreviews                                                   None   \n", "categories                                                             [Bags]   \n", "variantTranslations         {'ar': {'types': {'颜色': 'لون'}, 'names': {'米白色...   \n", "productUrl                    https://detail.1688.com/offer/820546352332.html   \n", "processedAt                                  2025-01-09T08:43:00.943000+00:00   \n", "failedImageDescriptions                                                  None   \n", "videoUrls                   [https://cloud.video.taobao.com/play/u/2218355...   \n", "processedBy                                                              None   \n", "failedImagePreviews                                                      None   \n", "infoProcessed                                                            True   \n", "offers                      [{'minQuantity': 1, 'quantityInfo': '1-499个', ...   \n", "mainImages                  [https://img.alicdn.com/imgextra/i3/**********...   \n", "claimedAt                                    2025-01-06T11:27:40.337000+00:00   \n", "updated                                      2025-01-09T08:43:00.943000+00:00   \n", "variants                    [{'variantName': '黑色', 'variantType': '颜色', 'a...   \n", "variantImages                                                              {}   \n", "processedVideos                                                           NaN   \n", "offerList                                                                 NaN   \n", "processingErrors                                                          NaN   \n", "variants_weight                                                           NaN   \n", "variantsWeight                                                            NaN   \n", "variants_details                                                          NaN   \n", "weightUnit_pants                                                          NaN   \n", "weight_pants                                                              NaN   \n", "\n", "                            ...  \\\n", "versionImages               ...   \n", "claimedBy                   ...   \n", "lastProcessed               ...   \n", "productImagePreviews        ...   \n", "processedImageDescriptions  ...   \n", "processingStatus            ...   \n", "customServices              ...   \n", "name                        ...   \n", "processingHistory           ...   \n", "attributes                  ...   \n", "productImageDescriptions    ...   \n", "serviceTranslations         ...   \n", "created                     ...   \n", "batchId                     ...   \n", "weightUnit                  ...   \n", "weight                      ...   \n", "translations                ...   \n", "processedImagePreviews      ...   \n", "categories                  ...   \n", "variantTranslations         ...   \n", "productUrl                  ...   \n", "processedAt                 ...   \n", "failedImageDescriptions     ...   \n", "videoUrls                   ...   \n", "processedBy                 ...   \n", "failedImagePreviews         ...   \n", "infoProcessed               ...   \n", "offers                      ...   \n", "mainImages                  ...   \n", "claimedAt                   ...   \n", "updated                     ...   \n", "variants                    ...   \n", "variantImages               ...   \n", "processedVideos             ...   \n", "offerList                   ...   \n", "processingErrors            ...   \n", "variants_weight             ...   \n", "variantsWeight              ...   \n", "variants_details            ...   \n", "weightUnit_pants            ...   \n", "weight_pants                ...   \n", "\n", "                                                         zzmLzXK9LP9jG7nCFAzn  \\\n", "versionImages                                                             NaN   \n", "claimedBy                                                                None   \n", "lastProcessed                                                             NaN   \n", "productImagePreviews        [https://cbu01.alicdn.com/img/ibank/O1CN01YClV...   \n", "processedImageDescriptions                                               None   \n", "processingStatus                                                  unprocessed   \n", "customServices                                                             []   \n", "name                                             5069-L380ERMS3 控制器 实现特定的控制功能   \n", "processingHistory                                                          []   \n", "attributes                  {'工作电压': '25', '型号': '5069-L380ERMS3', '安装型式':...   \n", "productImageDescriptions    [https://cbu01.alicdn.com/cms/upload/other/laz...   \n", "serviceTranslations                                                       NaN   \n", "created                                      2024-11-21T11:57:12.519000+00:00   \n", "batchId                                                                  None   \n", "weightUnit                                                                NaN   \n", "weight                                                                    NaN   \n", "translations                                                              NaN   \n", "processedImagePreviews                                                   None   \n", "categories                                                                NaN   \n", "variantTranslations                                                       NaN   \n", "productUrl                    https://detail.1688.com/offer/782354813899.html   \n", "processedAt                                                              None   \n", "failedImageDescriptions                                                  None   \n", "videoUrls                                                                  []   \n", "processedBy                                                              None   \n", "failedImagePreviews                                                      None   \n", "infoProcessed                                                           False   \n", "offers                      [{'minQuantity': 1, 'quantityInfo': '1个起批', 'c...   \n", "mainImages                                                                NaN   \n", "claimedAt                                                                None   \n", "updated                                                                   NaN   \n", "variants                    [{'variantName': '5069-L380ERMS3', 'variantTyp...   \n", "variantImages                                                             NaN   \n", "processedVideos                                                           NaN   \n", "offerList                                                                 NaN   \n", "processingErrors                                                          NaN   \n", "variants_weight                                                           NaN   \n", "variantsWeight                                                            NaN   \n", "variants_details                                                          NaN   \n", "weightUnit_pants                                                          NaN   \n", "weight_pants                                                              NaN   \n", "\n", "                                                         zzmVr6b6e2r0oME9qn85  \\\n", "versionImages                                                             NaN   \n", "claimedBy                                                                None   \n", "lastProcessed                                                             NaN   \n", "productImagePreviews        [https://img.alicdn.com/imgextra/i4/O1CN01SSxw...   \n", "processedImageDescriptions                                               None   \n", "processingStatus                                                  unprocessed   \n", "customServices                                                             []   \n", "name                                 厂家代发招5G随身wifi代理自插sim卡无线路由器6000mah四网通MiFi   \n", "processingHistory                                                          []   \n", "attributes                  {'产品尺寸': '136*72*16mm', '协议': '802.11ac', '货号'...   \n", "productImageDescriptions    [https://cbu01.alicdn.com/img/ibank/O1CN01orcG...   \n", "serviceTranslations                                                       NaN   \n", "created                                      2024-11-24T14:55:57.761000+00:00   \n", "batchId                                                                  None   \n", "weightUnit                                                                 kg   \n", "weight                                                                    0.8   \n", "translations                                                              NaN   \n", "processedImagePreviews                                                   None   \n", "categories                                                                NaN   \n", "variantTranslations                                                       NaN   \n", "productUrl                    https://detail.1688.com/offer/************.html   \n", "processedAt                                                              None   \n", "failedImageDescriptions                                                  None   \n", "videoUrls                                                                  []   \n", "processedBy                                                              None   \n", "failedImagePreviews                                                      None   \n", "infoProcessed                                                           False   \n", "offers                      [{'minQuantity': 1, 'quantityInfo': '1台起批', 'c...   \n", "mainImages                                                                NaN   \n", "claimedAt                                                                None   \n", "updated                                                                   NaN   \n", "variants                    [{'variantName': '内置电信5G流量卡/扫码充值', 'variantTyp...   \n", "variantImages                                                             NaN   \n", "processedVideos                                                           NaN   \n", "offerList                                                                 NaN   \n", "processingErrors                                                          NaN   \n", "variants_weight                                                           NaN   \n", "variantsWeight                                                            NaN   \n", "variants_details                                                          NaN   \n", "weightUnit_pants                                                          NaN   \n", "weight_pants                                                              NaN   \n", "\n", "                                                         zzoI1nGVuisSS9aGDaTl  \\\n", "versionImages                                                             NaN   \n", "claimedBy                                                                None   \n", "lastProcessed                                                             NaN   \n", "productImagePreviews        [https://img.alicdn.com/imgextra/i4/O1CN01SSxw...   \n", "processedImageDescriptions                                               None   \n", "processingStatus                                                  unprocessed   \n", "customServices                                                             []   \n", "name                                   inbrixx881101-03泰迪治愈咖啡店烘焙屋兼容乐高积木日式街景礼物   \n", "processingHistory                                                          []   \n", "attributes                                                                NaN   \n", "productImageDescriptions    [https://cbu01.alicdn.com/cms/upload/other/laz...   \n", "serviceTranslations                                                       NaN   \n", "created                                      2024-09-12T15:28:15.805000+00:00   \n", "batchId                                                                  None   \n", "weightUnit                                                                NaN   \n", "weight                                                                    NaN   \n", "translations                                                              NaN   \n", "processedImagePreviews                                                   None   \n", "categories                                                                NaN   \n", "variantTranslations                                                       NaN   \n", "productUrl                    https://detail.1688.com/offer/790437344179.html   \n", "processedAt                                                              None   \n", "failedImageDescriptions                                                  None   \n", "videoUrls                   [https://cloud.video.taobao.com/play/u/2217840...   \n", "processedBy                                                              None   \n", "failedImagePreviews                                                      None   \n", "infoProcessed                                                           False   \n", "offers                      [{'minQuantity': 1, 'quantityInfo': '1盒起批', 'c...   \n", "mainImages                                                                NaN   \n", "claimedAt                                                                None   \n", "updated                                                                   NaN   \n", "variants                    [{'variantName': 'inbrixx881101泰迪治愈咖啡屋', 'vari...   \n", "variantImages                                                             NaN   \n", "processedVideos                                                           NaN   \n", "offerList                                                                 NaN   \n", "processingErrors                                                          NaN   \n", "variants_weight                                                           NaN   \n", "variantsWeight                                                            NaN   \n", "variants_details                                                          NaN   \n", "weightUnit_pants                                                          NaN   \n", "weight_pants                                                              NaN   \n", "\n", "                                                         zzslsx6LmHTcyjCxpkn0  \\\n", "versionImages                                                             NaN   \n", "claimedBy                                                                None   \n", "lastProcessed                                                             NaN   \n", "productImagePreviews        [https://img.alicdn.com/imgextra/i1/**********...   \n", "processedImageDescriptions                                               None   \n", "processingStatus                                                  unprocessed   \n", "customServices                                                             []   \n", "name                                           工装裤子男春秋季新款潮牌宽松直筒裤潮流百搭青少年休闲阔腿长裤   \n", "processingHistory                                                          []   \n", "attributes                  {'风格': '简约', '主面料成分': '涤纶/涤纶（聚酯纤维）', '裤型': '宽松...   \n", "productImageDescriptions    [https://cbu01.alicdn.com/img/ibank/O1CN01P6tM...   \n", "serviceTranslations                                                       NaN   \n", "created                                      2024-11-05T11:53:35.368000+00:00   \n", "batchId                                                                  None   \n", "weightUnit                                                                  g   \n", "weight                                                                     20   \n", "translations                                                              NaN   \n", "processedImagePreviews                                                   None   \n", "categories                                                                NaN   \n", "variantTranslations                                                       NaN   \n", "productUrl                    https://detail.1688.com/offer/************.html   \n", "processedAt                                                              None   \n", "failedImageDescriptions                                                  None   \n", "videoUrls                   [https://cloud.video.taobao.com/play/u/2217268...   \n", "processedBy                                                              None   \n", "failedImagePreviews                                                      None   \n", "infoProcessed                                                           False   \n", "offers                      [{'minQuantity': 1, 'quantityInfo': '1件起批', 'c...   \n", "mainImages                                                                NaN   \n", "claimedAt                                                                None   \n", "updated                                                                   NaN   \n", "variants                    [{'variantName': 'M 90-105斤', 'variantType': '...   \n", "variantImages                                                             NaN   \n", "processedVideos                                                           NaN   \n", "offerList                                                                 NaN   \n", "processingErrors                                                          NaN   \n", "variants_weight                                                           NaN   \n", "variantsWeight                                                            NaN   \n", "variants_details                                                          NaN   \n", "weightUnit_pants                                                          NaN   \n", "weight_pants                                                              NaN   \n", "\n", "                                                         zzuEKsQKiHCQYtM1iDpo  \\\n", "versionImages                                                             NaN   \n", "claimedBy                                                                None   \n", "lastProcessed                                                             NaN   \n", "productImagePreviews        [https://img.alicdn.com/imgextra/i4/O1CN01SSxw...   \n", "processedImageDescriptions                                               None   \n", "processingStatus                                                  unprocessed   \n", "customServices                                                             []   \n", "name                        Casual PU Leather Business Shoulder Bag Crossb...   \n", "processingHistory                                                          []   \n", "attributes                                                                NaN   \n", "productImageDescriptions    [https://cbu01.alicdn.com/cms/upload/other/laz...   \n", "serviceTranslations                                                       NaN   \n", "created                                      2024-08-27T13:02:47.690000+00:00   \n", "batchId                                                                  None   \n", "weightUnit                                                                NaN   \n", "weight                                                                    NaN   \n", "translations                                                              NaN   \n", "processedImagePreviews                                                   None   \n", "categories                                                                NaN   \n", "variantTranslations                                                       NaN   \n", "productUrl                    https://detail.1688.com/offer/803334637109.html   \n", "processedAt                                                              None   \n", "failedImageDescriptions                                                  None   \n", "videoUrls                   [https://cloud.video.taobao.com/play/u/2217185...   \n", "processedBy                                                              None   \n", "failedImagePreviews                                                      None   \n", "infoProcessed                                                           False   \n", "offers                      [{'minQuantity': 1, 'quantityInfo': '1件起批', 'c...   \n", "mainImages                                                                NaN   \n", "claimedAt                                                                None   \n", "updated                                                                   NaN   \n", "variants                    [{'variantName': 'Bn', 'variantType': '颜色', 'a...   \n", "variantImages                                                             NaN   \n", "processedVideos                                                           NaN   \n", "offerList                                                                 NaN   \n", "processingErrors                                                          NaN   \n", "variants_weight                                                           NaN   \n", "variantsWeight                                                            NaN   \n", "variants_details                                                          NaN   \n", "weightUnit_pants                                                          NaN   \n", "weight_pants                                                              NaN   \n", "\n", "                                                         zzwG7bNxCIc0fFRSTYDH  \\\n", "versionImages                                                             NaN   \n", "claimedBy                                                                None   \n", "lastProcessed                                                             NaN   \n", "productImagePreviews        [https://cbu01.alicdn.com/img/ibank/O1CN01jyjs...   \n", "processedImageDescriptions                                               None   \n", "processingStatus                                                  unprocessed   \n", "customServices                                                             []   \n", "name                                         钢绞线锚索测力计 使用效率高 山东厂家直销 数显锚杆 锚索测力计   \n", "processingHistory                                                          []   \n", "attributes                  {'是否进口': '否', '测量精度': '99', '订货号': '354365', '...   \n", "productImageDescriptions    [https://cbu01.alicdn.com/img/ibank/O1CN01e3pg...   \n", "serviceTranslations                                                       NaN   \n", "created                                      2024-11-05T08:47:00.663000+00:00   \n", "batchId                                                                  None   \n", "weightUnit                                                                 kg   \n", "weight                                                                    0.5   \n", "translations                                                              NaN   \n", "processedImagePreviews                                                   None   \n", "categories                                                                NaN   \n", "variantTranslations                                                       NaN   \n", "productUrl                    https://detail.1688.com/offer/************.html   \n", "processedAt                                                              None   \n", "failedImageDescriptions                                                  None   \n", "videoUrls                   [https://cloud.video.taobao.com/play/u/2218009...   \n", "processedBy                                                              None   \n", "failedImagePreviews                                                      None   \n", "infoProcessed                                                           False   \n", "offers                      [{'minQuantity': 1, 'quantityInfo': '1套起批', 'c...   \n", "mainImages                                                                NaN   \n", "claimedAt                                                                None   \n", "updated                                                                   NaN   \n", "variants                    [{'variantName': '锚索的强度', 'variantType': '规格',...   \n", "variantImages                                                             NaN   \n", "processedVideos                                                           NaN   \n", "offerList                                                                 NaN   \n", "processingErrors                                                          NaN   \n", "variants_weight                                                           NaN   \n", "variantsWeight                                                            NaN   \n", "variants_details                                                          NaN   \n", "weightUnit_pants                                                          NaN   \n", "weight_pants                                                              NaN   \n", "\n", "                                                         zzxXByoHqEpKraAbM52H  \\\n", "versionImages                                                             NaN   \n", "claimedBy                                                                None   \n", "lastProcessed                                                             NaN   \n", "productImagePreviews        [https://cbu01.alicdn.com/img/ibank/O1CN01uvum...   \n", "processedImageDescriptions                                               None   \n", "processingStatus                                                  unprocessed   \n", "customServices                                                             []   \n", "name                                           黑色铁艺加粗三层锅盖架台面砧板菜板沥水置物架家用免打孔收纳架   \n", "processingHistory                                                          []   \n", "attributes                                                                NaN   \n", "productImageDescriptions                                                   []   \n", "serviceTranslations                                                       NaN   \n", "created                                      2024-09-16T16:51:19.811000+00:00   \n", "batchId                                                                  None   \n", "weightUnit                                                                NaN   \n", "weight                                                                    NaN   \n", "translations                                                              NaN   \n", "processedImagePreviews                                                   None   \n", "categories                                                                NaN   \n", "variantTranslations                                                       NaN   \n", "productUrl                    https://detail.1688.com/offer/766761512792.html   \n", "processedAt                                                              None   \n", "failedImageDescriptions                                                  None   \n", "videoUrls                                                                  []   \n", "processedBy                                                              None   \n", "failedImagePreviews                                                      None   \n", "infoProcessed                                                           False   \n", "offers                      [{'minQuantity': 1, 'quantityInfo': '10套成交', '...   \n", "mainImages                                                                NaN   \n", "claimedAt                                                                None   \n", "updated                                                                   NaN   \n", "variants                    [{'variantName': '三层', 'variantType': '层数（规格）'...   \n", "variantImages                                                             NaN   \n", "processedVideos                                                           NaN   \n", "offerList                                                                 NaN   \n", "processingErrors                                                          NaN   \n", "variants_weight                                                           NaN   \n", "variantsWeight                                                            NaN   \n", "variants_details                                                          NaN   \n", "weightUnit_pants                                                          NaN   \n", "weight_pants                                                              NaN   \n", "\n", "                                                         zzxxhlHSr3TuSaTb6iGV  \\\n", "versionImages                                                             NaN   \n", "claimedBy                                                                None   \n", "lastProcessed                                                             NaN   \n", "productImagePreviews        [https://cbu01.alicdn.com/img/ibank/O1CN01wvAe...   \n", "processedImageDescriptions                                               None   \n", "processingStatus                                                  unprocessed   \n", "customServices                                                             []   \n", "name                                           韩国跨境妈咪包大容量大号超轻妈妈待产包百搭儿童旅行手提化妆包   \n", "processingHistory                                                          []   \n", "attributes                  {'上市年份季节': '2023年冬季', '功能': '透气,耐磨,减负', '有可授权的...   \n", "productImageDescriptions    [https://cbu01.alicdn.com/img/ibank/O1CN013tON...   \n", "serviceTranslations                                                       NaN   \n", "created                                      2024-11-20T10:42:49.718000+00:00   \n", "batchId                                                                  None   \n", "weightUnit                                                                  g   \n", "weight                                                                    450   \n", "translations                                                              NaN   \n", "processedImagePreviews                                                   None   \n", "categories                                                                NaN   \n", "variantTranslations                                                       NaN   \n", "productUrl                    https://detail.1688.com/offer/************.html   \n", "processedAt                                                              None   \n", "failedImageDescriptions                                                  None   \n", "videoUrls                   [https://cloud.video.taobao.com/play/u/2213239...   \n", "processedBy                                                              None   \n", "failedImagePreviews                                                      None   \n", "infoProcessed                                                           False   \n", "offers                      [{'minQuantity': 2, 'quantityInfo': '2个起批', 'c...   \n", "mainImages                                                                NaN   \n", "claimedAt                                                                None   \n", "updated                                      2024-11-20T10:42:52.962000+00:00   \n", "variants                    [{'variantName': '小号', 'variantType': '颜色', 'a...   \n", "variantImages                                                             NaN   \n", "processedVideos                                                           NaN   \n", "offerList                                                                 NaN   \n", "processingErrors                                                          NaN   \n", "variants_weight                                                           NaN   \n", "variantsWeight                                                            NaN   \n", "variants_details                                                          NaN   \n", "weightUnit_pants                                                          NaN   \n", "weight_pants                                                              NaN   \n", "\n", "                                                         zzyL42O4ZvYQxSWJonHm  \\\n", "versionImages                                                             NaN   \n", "claimedBy                                                                None   \n", "lastProcessed                                                             NaN   \n", "productImagePreviews        [https://img.alicdn.com/imgextra/i4/O1CN01SSxw...   \n", "processedImageDescriptions                                               None   \n", "processingStatus                                                  unprocessed   \n", "customServices                                                             []   \n", "name                                           儿童过家玩具娃娃公仔玩偶女孩公主别墅拼装灯光声音模型城堡礼物   \n", "processingHistory                                                          []   \n", "attributes                                                                NaN   \n", "productImageDescriptions    [https://cbu01.alicdn.com/img/ibank/O1CN01j6JN...   \n", "serviceTranslations                                                       NaN   \n", "created                                      2024-10-07T20:04:28.748000+00:00   \n", "batchId                                                                  None   \n", "weightUnit                                                                NaN   \n", "weight                                                                    NaN   \n", "translations                                                              NaN   \n", "processedImagePreviews                                                   None   \n", "categories                                                                NaN   \n", "variantTranslations                                                       NaN   \n", "productUrl                    https://detail.1688.com/offer/************.html   \n", "processedAt                                                              None   \n", "failedImageDescriptions                                                  None   \n", "videoUrls                   [https://cloud.video.taobao.com/play/u/2208566...   \n", "processedBy                                                              None   \n", "failedImagePreviews                                                      None   \n", "infoProcessed                                                           False   \n", "offers                      [{'minQuantity': 2, 'quantityInfo': '2盒起批', 'c...   \n", "mainImages                                                                NaN   \n", "claimedAt                                                                None   \n", "updated                                                                   NaN   \n", "variants                    [{'variantName': '2层5房+灯', 'variantType': '规格'...   \n", "variantImages                                                             NaN   \n", "processedVideos                                                           NaN   \n", "offerList                                                                 NaN   \n", "processingErrors                                                          NaN   \n", "variants_weight                                                           NaN   \n", "variantsWeight                                                            NaN   \n", "variants_details                                                          NaN   \n", "weightUnit_pants                                                          NaN   \n", "weight_pants                                                              NaN   \n", "\n", "                                                         zzzuAwLvuYOGm38R40Ut  \n", "versionImages                                                             NaN  \n", "claimedBy                                                                None  \n", "lastProcessed                                                             NaN  \n", "productImagePreviews        [https://img.alicdn.com/imgextra/i4/O1CN01SSxw...  \n", "processedImageDescriptions                                               None  \n", "processingStatus                                                  unprocessed  \n", "customServices                                                             []  \n", "name                                        时尚网红同款无框太阳镜2020年新款四方潮墨镜女ins圆脸街拍眼镜  \n", "processingHistory                                                          []  \n", "attributes                                                                NaN  \n", "productImageDescriptions    [https://cbu01.alicdn.com/cms/upload/other/laz...  \n", "serviceTranslations                                                       NaN  \n", "created                                      2024-09-23T11:16:35.624000+00:00  \n", "batchId                                                                  None  \n", "weightUnit                                                                NaN  \n", "weight                                                                    NaN  \n", "translations                                                              NaN  \n", "processedImagePreviews                                                   None  \n", "categories                                                                NaN  \n", "variantTranslations                                                       NaN  \n", "productUrl                    https://detail.1688.com/offer/621448427113.html  \n", "processedAt                                                              None  \n", "failedImageDescriptions                                                  None  \n", "videoUrls                   [https://cloud.video.taobao.com/play/u/3206610...  \n", "processedBy                                                              None  \n", "failedImagePreviews                                                      None  \n", "infoProcessed                                                           False  \n", "offers                      [{'minQuantity': 1, 'quantityInfo': '1副', 'cur...  \n", "mainImages                                                                NaN  \n", "claimedAt                                                                None  \n", "updated                                                                   NaN  \n", "variants                    [{'variantName': '金框渐变灰', 'variantType': '镜片颜色...  \n", "variantImages                                                             NaN  \n", "processedVideos                                                           NaN  \n", "offerList                                                                 NaN  \n", "processingErrors                                                          NaN  \n", "variants_weight                                                           NaN  \n", "variantsWeight                                                            NaN  \n", "variants_details                                                          NaN  \n", "weightUnit_pants                                                          NaN  \n", "weight_pants                                                              NaN  \n", "\n", "[41 rows x 220262 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "id": "68f59f22", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "firebase", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}