#!/usr/bin/env python3
"""
Alibaba Data Processing Script

This script processes the exported Alibaba product data and transforms it
into the structure expected by our app's new schema with enhanced pricing,
variants, and provider data support.

Usage:
    python process_alibaba_data.py

Or in Jupyter notebook:
    %run process_alibaba_data.py

Output:
    - processed_products.json: Products ready for import
    - suppliers_to_create.json: Suppliers that need to be created
    - processing_report.json: Summary statistics and issues
"""

import json
import logging
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from datetime import datetime, timezone
import re
from collections import defaultdict

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class ProcessingStats:
    """Statistics for the data processing operation."""
    total_products: int = 0
    processed_products: int = 0
    failed_products: int = 0
    skipped_products: int = 0
    suppliers_found: int = 0
    products_with_variants: int = 0
    products_with_offers: int = 0
    products_with_images: int = 0

class AlibabaDataProcessor:
    """Processes Alibaba product data into our app's schema format."""

    def __init__(self, input_file: str = "data.json", output_dir: str = "."):
        self.input_file = input_file
        self.output_dir = output_dir
        self.stats = ProcessingStats()
        self.suppliers_to_create: Dict[str, Dict[str, Any]] = {}
        self.processed_products: List[Dict[str, Any]] = []
        self.failed_products: List[Dict[str, Any]] = []

        # Currency mapping
        self.currency_mapping = {
            '¥': 'CNY',
            'CNY': 'CNY',
            'USD': 'USD',
            '$': 'USD',
            'EUR': 'EUR',
            '€': 'EUR'
        }

    def load_data(self) -> Dict[str, Any]:
        """Load the Alibaba data from JSON file."""
        logger.info(f"Loading data from {self.input_file}")
        try:
            with open(self.input_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            self.stats.total_products = len(data)
            logger.info(f"Loaded {self.stats.total_products} products")
            return data
        except Exception as e:
            logger.error(f"Failed to load data: {e}")
            raise

    def extract_supplier_info(self, product: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Extract supplier information from product data."""
        # Try to extract supplier from productUrl or attributes
        supplier_info = {}

        if 'productUrl' in product and product['productUrl']:
            # Extract domain or supplier identifier from URL
            url_match = re.search(r'https?://(?:www\.)?([^./]+)', product['productUrl'])
            if url_match:
                supplier_info['platformUrl'] = product['productUrl']
                supplier_info['name'] = url_match.group(1).title()

        # Look for supplier info in attributes
        attributes = product.get('attributes', {})
        if isinstance(attributes, dict):
            supplier_name = attributes.get('品牌', attributes.get('brand', attributes.get('Brand')))
            if supplier_name:
                supplier_info['name'] = supplier_name

        if supplier_info:
            supplier_key = supplier_info.get('name', supplier_info.get('platformUrl', 'unknown'))
            if supplier_key not in self.suppliers_to_create:
                self.suppliers_to_create[supplier_key] = supplier_info
                self.stats.suppliers_found += 1

            return supplier_key

        return None

    def process_offers(self, offers: Union[List, Dict, None]) -> List[Dict[str, Any]]:
        """Process offers into pricingTiers format."""
        pricing_tiers: List[Dict[str, Any]] = []

        if not offers:
            return pricing_tiers

        try:
            if isinstance(offers, list):
                # Shape A: Array of Offer objects
                for offer in offers:
                    if not isinstance(offer, dict):
                        continue

                    tier = {
                        'minQuantity': offer.get('minQuantity', 1),
                        'maxQuantity': None,  # Alibaba doesn't specify max
                        'price': self._parse_price(offer.get('price')),
                        'currency': self._normalize_currency(offer.get('currency', 'CNY')),
                        'discountPercentage': None
                    }
                    pricing_tiers.append(tier)

            elif isinstance(offers, dict) and 'pricing_tiers' in offers:
                # Shape B: Object with pricing_tiers
                for tier_data in offers['pricing_tiers']:
                    if not isinstance(tier_data, dict):
                        continue

                    tier = {
                        'minQuantity': int(tier_data.get('minQuantity', 1)),
                        'maxQuantity': None,
                        'price': float(tier_data.get('price', 0)),
                        'currency': self._normalize_currency(tier_data.get('currency', 'CNY')),
                        'discountPercentage': None
                    }
                    pricing_tiers.append(tier)

            self.stats.products_with_offers += 1

        except Exception as e:
            logger.warning(f"Error processing offers: {e}")

        return pricing_tiers

    def process_variants(self, variants: Union[List, Dict, None]) -> List[Dict[str, Any]]:
        """Process variants into our variants format."""
        processed_variants: List[Dict[str, Any]] = []

        if not variants:
            return processed_variants

        try:
            if isinstance(variants, list):
                # Shape A: Array of Variant objects
                for variant in variants:
                    if not isinstance(variant, dict):
                        continue

                    processed_variant = {
                        'type': variant.get('variantType', 'option'),
                        'name': variant.get('variantName', ''),
                        'value': variant.get('variantName', ''),
                        'priceType': 'absolute' if variant.get('price') else 'modifier',
                        'absolutePrice': self._parse_price(variant.get('price')) if variant.get('price') else None,
                        'priceModifier': self._parse_price(variant.get('price')) if variant.get('price') else 0,
                        'currency': self._normalize_currency(variant.get('currency')),
                        'availableQuantity': variant.get('availableQuantity'),
                        'images': []  # Will be populated from variantImages if available
                    }
                    processed_variants.append(processed_variant)

            elif isinstance(variants, dict) and 'variants' in variants:
                # Shape B: Object with variants list
                for variant_data in variants['variants']:
                    if not isinstance(variant_data, dict):
                        continue

                    processed_variant = {
                        'type': 'color' if variant_data.get('variantType') == '颜色' else
                               'size' if variant_data.get('variantType') == '尺码' else 'option',
                        'name': variant_data.get('variantName', ''),
                        'value': variant_data.get('variantName', ''),
                        'priceType': 'absolute',
                        'absolutePrice': float(variant_data.get('price', 0)),
                        'priceModifier': 0,
                        'currency': self._normalize_currency(variant_data.get('currency', 'CNY')),
                        'availableQuantity': int(variant_data.get('availableQuantity', 0)),
                        'images': []
                    }
                    processed_variants.append(processed_variant)

            if processed_variants:
                self.stats.products_with_variants += 1

        except Exception as e:
            logger.warning(f"Error processing variants: {e}")

        return processed_variants

    def process_images(self, product: Dict[str, Any]) -> List[str]:
        """Extract and prioritize product images."""
        images = []

        # Priority: productImagePreviews > productImageDescriptions > mainImages
        image_sources = [
            product.get('productImagePreviews', []),
            product.get('productImageDescriptions', []),
            product.get('mainImages', [])
        ]

        for source in image_sources:
            if isinstance(source, list) and source:
                images.extend([img for img in source if img and isinstance(img, str)])
                break  # Use first available source

        if images:
            self.stats.products_with_images += 1

        return images[:10]  # Limit to first 10 images

    def process_attributes(self, attributes: Union[Dict, None]) -> Dict[str, Any]:
        """Process product attributes into structured format."""
        if not isinstance(attributes, dict):
            return {}

        processed = {}

        # Map common attributes
        attribute_mapping = {
            '品牌': 'brand',
            '风格': 'style',
            '颜色': 'color',
            '尺寸': 'size',
            '材质': 'material',
            '重量': 'weight',
            '产地': 'origin',
            '是否进口': 'is_imported'
        }

        for key, value in attributes.items():
            if key in attribute_mapping:
                processed[attribute_mapping[key]] = value
            else:
                # Keep original key for unmapped attributes
                processed[key] = value

        return processed

    def _parse_price(self, price: Any) -> Optional[float]:
        """Parse price value that can be string, number, or range."""
        if price is None:
            return None

        try:
            if isinstance(price, (int, float)):
                return float(price)
            elif isinstance(price, str):
                # Handle price ranges like "10-20" or "10.5"
                if '-' in price:
                    # Take the lower bound for ranges
                    return float(price.split('-')[0].strip())
                else:
                    return float(re.sub(r'[^\d.]', '', price))
            elif isinstance(price, list):
                # Take first price if it's a list
                return self._parse_price(price[0]) if price else None
        except (ValueError, IndexError):
            pass

        return None

    def _normalize_currency(self, currency: Optional[str]) -> str:
        """Normalize currency codes."""
        if not currency:
            return 'CNY'

        return self.currency_mapping.get(currency.strip(), currency.upper())

    def transform_product(self, product_id: str, product: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Transform a single Alibaba product to our app's format."""
        try:
            # Extract supplier information
            supplier_id = self.extract_supplier_info(product)

            # Process pricing tiers
            pricing_tiers = self.process_offers(product.get('offers'))

            # If no pricing tiers, create a default one from the product data
            if not pricing_tiers:
                default_price = self._parse_price(product.get('price'))
                if default_price:
                    pricing_tiers = [{
                        'minQuantity': 1,
                        'maxQuantity': None,
                        'price': default_price,
                        'currency': 'CNY',
                        'discountPercentage': None
                    }]

            # Process variants
            variants = self.process_variants(product.get('variants'))

            # Process custom services
            custom_services = []
            if product.get('customServices'):
                for service in product['customServices']:
                    if isinstance(service, dict):
                        custom_services.append({
                            'name': service.get('serviceName', ''),
                            'description': service.get('serviceType', ''),
                            'minQuantity': service.get('minQuantity', 1),
                            'price': self._parse_price(service.get('price', 0)),
                            'currency': self._normalize_currency(service.get('currency', 'CNY')),
                            'isRequired': False
                        })

            # Process images
            images = self.process_images(product)

            # Process attributes
            attributes = self.process_attributes(product.get('attributes'))

            # Create transformed product
            transformed = {
                'title': product.get('name', ''),
                'description': '',  # Will be filled from image descriptions or attributes
                'curationNotes': '',  # To be filled manually by admins
                'supplierId': supplier_id,
                'priceInYuan': pricing_tiers[0]['price'] if pricing_tiers else 0,
                'serviceFee': 0,  # To be calculated by business logic
                'finalPrice': pricing_tiers[0]['price'] if pricing_tiers else 0,
                'tags': [],  # To be generated from attributes
                'images': images,
                'imageEmbedding': None,  # Will be generated after import
                'stockCount': 100,  # Default stock, to be updated by admins
                'status': 'active' if product.get('processingStatus') == 'processed' else 'inactive',
                'providerData': {
                    'source': 'alibaba',
                    'productUrl': product.get('productUrl', ''),
                    'providerId': product_id,
                    'lastScraped': int(datetime.now(timezone.utc).timestamp() * 1000),
                    'providerSpecificData': None
                },
                'pricingTiers': pricing_tiers,
                'variants': variants,
                'customServices': custom_services,
                'attributes': attributes,
                'createdBy': 'system',  # Placeholder
                'updatedBy': 'system'   # Placeholder
            }

            return transformed

        except Exception as e:
            logger.error(f"Failed to transform product {product_id}: {e}")
            self.failed_products.append({
                'product_id': product_id,
                'error': str(e),
                'product_name': product.get('name', 'Unknown')
            })
            return None

    def process_all_products(self):
        """Process all products from the input file."""
        logger.info("Starting data processing...")

        data = self.load_data()

        for product_id, product in data.items():
            transformed = self.transform_product(product_id, product)
            if transformed:
                self.processed_products.append(transformed)
                self.stats.processed_products += 1
            else:
                self.stats.failed_products += 1

        logger.info(f"Processing complete: {self.stats.processed_products} processed, {self.stats.failed_products} failed")

    def generate_report(self) -> Dict[str, Any]:
        """Generate processing report."""
        return {
            'stats': {
                'total_products': self.stats.total_products,
                'processed_products': self.stats.processed_products,
                'failed_products': self.stats.failed_products,
                'suppliers_found': self.stats.suppliers_found,
                'products_with_variants': self.stats.products_with_variants,
                'products_with_offers': self.stats.products_with_offers,
                'products_with_images': self.stats.products_with_images
            },
            'suppliers_to_create': list(self.suppliers_to_create.values()),
            'failed_products': self.failed_products[:100]  # Limit for report
        }

    def save_results(self):
        """Save processing results to files."""
        logger.info("Saving results...")

        # Save processed products
        with open(f"{self.output_dir}/processed_products.json", 'w', encoding='utf-8') as f:
            json.dump(self.processed_products, f, indent=2, ensure_ascii=False)

        # Save suppliers to create
        with open(f"{self.output_dir}/suppliers_to_create.json", 'w', encoding='utf-8') as f:
            json.dump(list(self.suppliers_to_create.values()), f, indent=2, ensure_ascii=False)

        # Save processing report
        report = self.generate_report()
        with open(f"{self.output_dir}/processing_report.json", 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        logger.info(f"Results saved to {self.output_dir}")

def main():
    """Main function to run the data processing."""
    processor = AlibabaDataProcessor()
    processor.process_all_products()
    processor.save_results()

    # Print summary
    report = processor.generate_report()
    stats = report['stats']

    print("\n" + "="*60)
    print("ALIBABA DATA PROCESSING COMPLETE")
    print("="*60)
    print(f"Total Products: {stats['total_products']}")
    print(f"Processed: {stats['processed_products']}")
    print(f"Failed: {stats['failed_products']}")
    print(f"Suppliers Found: {stats['suppliers_found']}")
    print(f"Products with Variants: {stats['products_with_variants']}")
    print(f"Products with Offers: {stats['products_with_offers']}")
    print(f"Products with Images: {stats['products_with_images']}")
    print("="*60)

if __name__ == "__main__":
    main()