### 1. The Main Product Structure (`AlibabaProduct`)

This is the core object for a single product.

```typescript
// Represents the main product object
class AlibabaProduct {
  // --- Core Details (Reliable) ---
  name: string;
  productUrl: string; // URL to the product page
  created: Date;      // When the product was first scraped/created
  updated: Date;      // When the product was last updated

  // --- Media (Read Notes Carefully) ---
  productImagePreviews?: string[];    // [Primary Source] Use this for all general product imagery.
  productImageDescriptions?: string[];  // [Primary Source] Use this for images containing technical descriptions.
  mainImages: string[];              // [Unreliable] Often empty or duplicates `productImagePreviews`. Do not use.
  versionImages?: string[];             // [Unreliable] Often empty. Do not use.
  variantImages: { [variantName: string]: string[] }; // [Unreliable] Often empty. Do not use.
  videoUrls?: string[];               // Use if available, but handle cases where it's empty.

  // --- Pricing & Variants (Reliable & Important) ---
  offers: Offer[] | OfferTiers;       // Critical for pricing logic. See notes below.
  variants: Variant[] | VariantTiers; // Defines product variant and their price.
  offerList?: OfferList[];            // A simpler list of offers, can be used if `offers` is not available.
  
  // --- Attributes & Services (Use as Needed) ---
  attributes: { [key: string]: string | string[] | number | boolean | null }; // Useful for product specifications and filters.
  customServices?: CustomService[]; // List of customization services.
  weight?: number | { [key: string]: number } | null; // Product weight.
  weightUnit?: WeightUnit;            // Enum: "g", "kg", etc.

  // --- Processing & Admin (Internal Use) ---
  processingStatus: "processed" | "processing" | "failed" | "unprocessed" | null;
  lastProcessed?: Date;
  claimedBy?: string | null;          // ID of the user/system that claimed this item
  claimedAt?: Date | null;
  processingHistory?: ProcessingHistoryEntry[];
  processingErrors?: ProcessingErrors;
  
  // --- Internationalization (IGNORE) ---
  // [Ignore] Data is not provided in the source. Do not use.
  translations?: Translations;
  variantTranslations?: VariantTranslations;
  serviceTranslations?: ServiceTranslations;
}
```

---

### 2. Complex Structures Explained

Your data contains fields that can have more than one shape. This is common in real-world data. Your app will need logic to check which structure it's dealing with.

#### Offers: Pricing Tiers (`offers`)

The `offers` property can be either:
**A)** A simple array of `Offer` objects.
**B)** A single object containing pricing tiers.

```typescript
// Shape A: A simple list of offers
class Offer {
  minQuantity: number;
  price: number | number[] | string | null; // Price can be a single number, a range, or a string
  currency: string | null; // e.g., "CNY", "USD"
  quantityInfo: string | null; // e.g., "pieces"
}

// Shape B: An object with pricing tiers
class OfferTiers {
  pricing_tiers: PricingTier[];
}

class PricingTier {
  minQuantity: string;
  price: string;
  currency: string;
  quantityInfo: string;
}
```
**Developer Action:** When you access `product.offers`, you must first check if it's an array or an object to know how to parse it.

#### Variants: Product Options (`variants`)

Similarly, the `variants` property can be either:
**A)** A simple array of `Variant` objects.
**B)** A single object containing a list of variants.

```typescript
// Shape A: A simple list of product variants
class Variant {
  variantName: string | null;     // e.g., "Red", "Large"
  variantType: string | null;     // e.g., "Color", "Size"
  availableQuantity?: number | string | null;
  price?: number | number[] | string | null;
  currency?: string | null;
  minQuantity?: number | null;
  // ... and other optional fields like sizeInfo, weight, etc.
}

// Shape B: An object with a variants list
class VariantTiers {
  variants: VariantTier[];
}

class VariantTier {
  variantName: string;
  variantType: "颜色" | "尺码"; // "Color" or "Size" in Chinese
  price: string;
  availableQuantity: string;
  currency: string;
}
```
**Developer Action:** Just like with offers, you need to check if `product.variants` is an array or an object before processing it.

---

### 3. Supporting Data Models

These are the smaller, reusable structures.

```typescript
// A customization service offered for the product
class CustomService {
  serviceName: string | null;    // e.g., "Customized logo"
  serviceType: string | null;
  minQuantity?: number | string | null;
  price?: number | string | null;
  currency?: string | null;
}

// An entry in the processing history log
class ProcessingHistoryEntry {
  step: "image-selection" | "extraction" | "translation" | ...; // The processing step
  status: "completed" | "processing";
  timestamp: Date;
}

// Translated content for different languages
class Translations {
  ar?: LanguageTranslation; // Arabic
  fr?: LanguageTranslation; // French
  en?: LanguageTranslation; // English
}

class LanguageTranslation {
  name: string;
  description: string;
  attributes: { [key: string]: string }; // Translated attribute names and values
}

// Enum for weight units (a selection of common values)
enum WeightUnit {
  G = "g",
  KG = "kg",
  T = "t",
  // ... many other variations exist in your schema ("克", "公斤", etc.)
}

// Details on processing errors
class ProcessingErrors {
  generalError?: string;
  videoErrors?: any[];
  descriptionErrors?: ErrorDetail[];
  previewErrors?: ErrorDetail[];
}

class ErrorDetail {
  error: string; // The error message
  url: string;   // The URL of the asset that failed
}
```