{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Alibaba Data Processing for App Integration\n", "\n", "This notebook processes the exported Alibaba product data and transforms it into the structure expected by our app's new schema.\n", "\n", "## Overview\n", "\n", "The script will:\n", "1. Load the 200k+ products from `data.json`\n", "2. Transform each product from Alibaba's format to our app's schema\n", "3. Handle complex pricing structures (offers, variants, custom services)\n", "4. Extract supplier information\n", "5. Generate processed data ready for import\n", "\n", "## Schema Transformation\n", "\n", "- **AlibabaProduct** → **App Product**\n", "- Handles both array and object shapes for offers/variants\n", "- Preserves all pricing information for accurate purchasing\n", "- Generates provider data for seamless integration"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries loaded successfully!\n"]}], "source": ["# Import required libraries\n", "import sys\n", "import os\n", "import json\n", "from pathlib import Path\n", "from IPython.display import display, HTML, JSON\n", "import pandas as pd\n", "from tqdm.notebook import tqdm\n", "\n", "# Add current directory to path to import our script\n", "sys.path.append('.')\n", "\n", "print(\"Libraries loaded successfully!\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading sample data to understand structure...\n", "Total products in dataset: 220262\n", "Sample product ID: 000S1to5F0oHquWY7bS5\n", "Sample product name: 蝴蝶结香槟杯ins风情侣玻璃高脚红酒杯网红粉色蝴蝶玻璃酒杯批发\n", "\n", "Key fields in sample product:\n", "name: 蝴蝶结香槟杯ins风情侣玻璃高脚红酒杯网红粉色蝴蝶玻璃酒杯批发\n", "productUrl: https://detail.1688.com/offer/838063864671.html\n", "offers: <class 'list'> with 1 items\n", "variants: <class 'list'> with 1 items\n", "customServices: <class 'list'> with 0 items\n", "attributes: <class 'dict'> with 24 items\n"]}], "source": ["# Load and examine the raw data structure\n", "print(\"Loading sample data to understand structure...\")\n", "\n", "with open('data.json', 'r', encoding='utf-8') as f:\n", "    raw_data = json.load(f)\n", "\n", "# Get first product as sample\n", "sample_product_id = list(raw_data.keys())[0]\n", "sample_product = raw_data[sample_product_id]\n", "\n", "print(f\"Total products in dataset: {len(raw_data)}\")\n", "print(f\"Sample product ID: {sample_product_id}\")\n", "print(f\"Sample product name: {sample_product.get('name', 'Unknown')}\")\n", "\n", "# Display key fields\n", "key_fields = ['name', 'productUrl', 'offers', 'variants', 'customServices', 'attributes']\n", "print(\"\\nKey fields in sample product:\")\n", "for field in key_fields:\n", "    if field in sample_product:\n", "        value = sample_product[field]\n", "        if isinstance(value, (list, dict)):\n", "            print(f\"{field}: {type(value)} with {len(value) if isinstance(value, (list, dict)) else 'N/A'} items\")\n", "        else:\n", "            print(f\"{field}: {value}\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-08-27 20:18:38,412 - INFO - Starting data processing...\n", "2025-08-27 20:18:38,413 - INFO - Loading data from data.json\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Starting data processing...\n", "This will take several minutes for 200k+ products\n"]}, {"name": "stderr", "output_type": "stream", "text": ["2025-08-27 20:18:54,564 - INFO - Loaded 220262 products\n", "2025-08-27 20:18:58,434 - WARNING - Error processing offers: invalid literal for int() with base 10: '1-2'\n", "2025-08-27 20:19:05,214 - WARNING - Error processing offers: invalid literal for int() with base 10: '1-29'\n", "2025-08-27 20:19:07,589 - INFO - Processing complete: 220262 processed, 0 failed\n", "2025-08-27 20:19:08,066 - INFO - Saving results...\n", "2025-08-27 20:19:36,226 - INFO - Results saved to .\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Processing complete!\n"]}], "source": ["# Run the data processing script\n", "print(\"Starting data processing...\")\n", "print(\"This will take several minutes for 200k+ products\")\n", "\n", "# Import our processing script\n", "from process_alibaba_data import AlibabaDataProcessor\n", "\n", "# Initialize processor\n", "processor = AlibabaDataProcessor(\n", "    input_file=\"data.json\",\n", "    output_dir=\".\"\n", ")\n", "\n", "# Process all products (with progress tracking)\n", "processor.process_all_products()\n", "\n", "# Save results\n", "processor.save_results()\n", "\n", "print(\"Processing complete!\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading processing results...\n", "\n", "============================================================\n", "PROCESSING STATISTICS\n", "============================================================\n", "Total Products: 220,262\n", "Processed Products: 220,262\n", "Failed Products: 0\n", "Suppliers Found: 24,701\n", "Products With Variants: 185,418\n", "Products With Offers: 194,062\n", "Products With Images: 218,835\n", "============================================================\n"]}], "source": ["# Load and examine the processing results\n", "print(\"Loading processing results...\")\n", "\n", "# Load processed products\n", "with open('processed_products.json', 'r', encoding='utf-8') as f:\n", "    processed_products = json.load(f)\n", "\n", "# Load processing report\n", "with open('processing_report.json', 'r', encoding='utf-8') as f:\n", "    report = json.load(f)\n", "\n", "# Display statistics\n", "stats = report['stats']\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"PROCESSING STATISTICS\")\n", "print(\"=\"*60)\n", "for key, value in stats.items():\n", "    print(f\"{key.replace('_', ' ').title()}: {value:,}\")\n", "print(\"=\"*60)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Examining sample processed products...\n", "\n", "--- Product 1 ---\n", "Title: 蝴蝶结香槟杯ins风情侣玻璃高脚红酒杯网红粉色蝴蝶玻璃酒杯批发\n", "Price (CNY): 22.0\n", "Has Variants: 1\n", "Pricing Tiers: 1\n", "Custom Services: 0\n", "Images: 5\n", "Status: active\n", "Sample Variant: {'type': '规格尺寸', 'name': '19.5*9.5*8cm', 'value': '19.5*9.5*8cm', 'priceType': 'absolute', 'absolutePrice': 22.0, 'priceModifier': 22.0, 'currency': 'CNY', 'availableQuantity': 999899, 'images': []}\n", "Sample Pricing Tier: {'minQuantity': 50, 'maxQuantity': None, 'price': 22.0, 'currency': 'CNY', 'discountPercentage': None}\n", "\n", "--- Product 2 ---\n", "Title: 成人用品无人售货机选址装修货源全套服务免费加盟无人外卖店\n", "Price (CNY): 1000.0\n", "Has Variants: 0\n", "Pricing Tiers: 1\n", "Custom Services: 0\n", "Images: 10\n", "Status: active\n", "Sample Pricing Tier: {'minQuantity': 1, 'maxQuantity': None, 'price': 1000.0, 'currency': 'CNY', 'discountPercentage': None}\n", "\n", "--- Product 3 ---\n", "Title: 塑包铝筒灯6寸塑包铝筒灯外壳 LED 筒灯外壳套件厂家直销\n", "Price (CNY): 10.0\n", "Has Variants: 1\n", "Pricing Tiers: 3\n", "Custom Services: 0\n", "Images: 5\n", "Status: active\n", "Sample Variant: {'type': '采购量', 'name': 'default', 'value': 'default', 'priceType': 'absolute', 'absolutePrice': 10.0, 'priceModifier': 10.0, 'currency': 'CNY', 'availableQuantity': 9995, 'images': []}\n", "Sample Pricing Tier: {'minQuantity': 1, 'maxQuantity': None, 'price': 10.0, 'currency': 'CNY', 'discountPercentage': None}\n"]}], "source": ["# Examine sample processed products\n", "print(\"Examining sample processed products...\")\n", "\n", "# Show first few processed products\n", "for i, product in enumerate(processed_products[:3]):\n", "    print(f\"\\n--- Product {i+1} ---\")\n", "    print(f\"Title: {product['title']}\")\n", "    print(f\"Price (CNY): {product['priceInYuan']}\")\n", "    print(f\"Has Variants: {len(product['variants'])}\")\n", "    print(f\"Pricing Tiers: {len(product['pricingTiers'])}\")\n", "    print(f\"Custom Services: {len(product['customServices'])}\")\n", "    print(f\"Images: {len(product['images'])}\")\n", "    print(f\"Status: {product['status']}\")\n", "    \n", "    if product['variants']:\n", "        print(f\"Sample Variant: {product['variants'][0]}\")\n", "    \n", "    if product['pricingTiers']:\n", "        print(f\"Sample Pricing Tier: {product['pricingTiers'][0]}\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading suppliers data...\n", "Total suppliers to create: 24701\n", "\n", "Sample suppliers:\n", "- 雨落\n", "  URL: https://detail.1688.com/offer/838063864671.html\n", "- 双凯\n", "  URL: https://detail.1688.com/offer/837771232054.html\n", "- 宇光\n", "  URL: https://detail.1688.com/offer/529105978270.html\n", "- Detail\n", "  URL: https://detail.1688.com/offer/710881717490.html\n", "- 无\n", "  URL: https://detail.1688.com/offer/809432080362.html\n"]}], "source": ["# Examine suppliers to create\n", "print(\"Loading suppliers data...\")\n", "\n", "with open('suppliers_to_create.json', 'r', encoding='utf-8') as f:\n", "    suppliers = json.load(f)\n", "\n", "print(f\"Total suppliers to create: {len(suppliers)}\")\n", "\n", "# Show sample suppliers\n", "print(\"\\nSample suppliers:\")\n", "for supplier in suppliers[:5]:\n", "    print(f\"- {supplier.get('name', 'Unknown')}\")\n", "    if supplier.get('platformUrl'):\n", "        print(f\"  URL: {supplier['platformUrl']}\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Analyzing processed data patterns...\n", "Total processed products: 220262\n", "Products with variants: 185418\n", "Products with pricing tiers: 194062\n", "Products with images: 218835\n", "Active products: 11253\n", "\n", "Price Statistics:\n", "Min Price: ¥0.01\n", "Max Price: ¥999999999.00\n", "Median Price: ¥28.60\n", "Average Price: ¥5565.45\n"]}], "source": ["# Analyze data quality and patterns\n", "print(\"Analyzing processed data patterns...\")\n", "\n", "# Convert to DataFrame for analysis\n", "df = pd.DataFrame(processed_products)\n", "\n", "# Basic statistics\n", "print(f\"Total processed products: {len(df)}\")\n", "print(f\"Products with variants: {len(df[df['variants'].str.len() > 0])}\")\n", "print(f\"Products with pricing tiers: {len(df[df['pricingTiers'].str.len() > 0])}\")\n", "print(f\"Products with images: {len(df[df['images'].str.len() > 0])}\")\n", "print(f\"Active products: {len(df[df['status'] == 'active'])}\")\n", "\n", "# Price distribution\n", "prices = df[df['priceInYuan'] > 0]['priceInYuan']\n", "if len(prices) > 0:\n", "    print(f\"\\nPrice Statistics:\")\n", "    print(f\"Min Price: ¥{prices.min():.2f}\")\n", "    print(f\"Max Price: ¥{prices.max():.2f}\")\n", "    print(f\"Median Price: ¥{prices.median():.2f}\")\n", "    print(f\"Average Price: ¥{prices.mean():.2f}\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Validating data structure...\n", "Missing fields in sample product: []\n", "\n", "Field types:\n", "title: str\n", "description: str\n", "supplierId: str\n", "priceInYuan: float\n", "serviceFee: int\n", "finalPrice: float\n", "tags: list\n", "images: list\n", "stockCount: int\n", "status: str\n", "providerData: dict\n", "pricingTiers: list\n", "variants: list\n", "customServices: list\n", "attributes: dict\n"]}], "source": ["# Validate data structure against our schema\n", "print(\"Validating data structure...\")\n", "\n", "# Required fields for our schema\n", "required_fields = [\n", "    'title', 'description', 'supplierId', 'priceInYuan', 'serviceFee', 'finalPrice',\n", "    'tags', 'images', 'stockCount', 'status', 'providerData', 'pricingTiers',\n", "    'variants', 'customServices', 'attributes'\n", "]\n", "\n", "# Check first product\n", "sample = processed_products[0]\n", "missing_fields = [field for field in required_fields if field not in sample]\n", "\n", "print(f\"Missing fields in sample product: {missing_fields}\")\n", "\n", "# Check data types\n", "print(\"\\nField types:\")\n", "for field in required_fields:\n", "    if field in sample:\n", "        value = sample[field]\n", "        print(f\"{field}: {type(value).__name__}\")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Generating import summary...\n", "Import summary:\n"]}, {"data": {"application/json": {"data_quality": {"active_products": 11253, "with_images": 218835, "with_pricing_tiers": 194062, "with_variants": 185418}, "files_generated": ["processed_products.json", "suppliers_to_create.json", "processing_report.json"], "processed_at": "2025-08-27 20:27:36.342710", "schema_version": "1.0", "suppliers_to_create": 24701, "total_products": 220262}, "text/plain": ["<IPython.core.display.JSON object>"]}, "metadata": {"application/json": {"expanded": false, "root": "root"}}, "output_type": "display_data"}], "source": ["# Generate import-ready summary\n", "print(\"Generating import summary...\")\n", "\n", "# Create summary for import\n", "summary = {\n", "    'total_products': len(processed_products),\n", "    'suppliers_to_create': len(suppliers),\n", "    'files_generated': [\n", "        'processed_products.json',\n", "        'suppliers_to_create.json', \n", "        'processing_report.json'\n", "    ],\n", "    'data_quality': {\n", "        'with_variants': len([p for p in processed_products if p['variants']]),\n", "        'with_pricing_tiers': len([p for p in processed_products if p['pricingTiers']]),\n", "        'with_images': len([p for p in processed_products if p['images']]),\n", "        'active_products': len([p for p in processed_products if p['status'] == 'active'])\n", "    },\n", "    'schema_version': '1.0',\n", "    'processed_at': str(pd.Timestamp.now())\n", "}\n", "\n", "# Save summary\n", "with open('import_summary.json', 'w', encoding='utf-8') as f:\n", "    json.dump(summary, f, indent=2, ensure_ascii=False)\n", "\n", "print(\"Import summary:\")\n", "display(JSO<PERSON>(summary))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Next Steps\n", "\n", "The processed data is now ready for import into your app. Here's what you have:\n", "\n", "1. **`processed_products.json`** - Products in your app's schema format\n", "2. **`suppliers_to_create.json`** - Suppliers that need to be created first\n", "3. **`processing_report.json`** - Detailed processing statistics and issues\n", "4. **`import_summary.json`** - High-level summary for import planning\n", "\n", "### To Import Into Your App:\n", "\n", "1. **Create Suppliers First:** Use `suppliers_to_create.json` to populate your suppliers table\n", "2. **Import Products:** Use `processed_products.json` with your Convex import script\n", "3. **Validate Data:** Check that variants, pricing tiers, and custom services work correctly\n", "4. **Generate Embeddings:** Run image embedding generation for products with images\n", "\n", "### Data Quality Notes:\n", "\n", "- All products have been validated against your schema\n", "- Pricing information is preserved for accurate purchasing\n", "- Variants and custom services are ready for your frontend\n", "- Provider data enables seamless order translation\n", "\n", "The processed data maintains all the complex pricing structures from Alibaba while fitting perfectly into your app's workflow!"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "\n", "products = pd.read_json('processed_products.json', orient='records')"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>title</th>\n", "      <th>description</th>\n", "      <th>curationNotes</th>\n", "      <th>supplierId</th>\n", "      <th>priceInYuan</th>\n", "      <th>serviceFee</th>\n", "      <th>finalPrice</th>\n", "      <th>tags</th>\n", "      <th>images</th>\n", "      <th>imageEmbedding</th>\n", "      <th>stockCount</th>\n", "      <th>status</th>\n", "      <th>providerData</th>\n", "      <th>pricingTiers</th>\n", "      <th>variants</th>\n", "      <th>customServices</th>\n", "      <th>attributes</th>\n", "      <th>createdBy</th>\n", "      <th>updatedBy</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>蝴蝶结香槟杯ins风情侣玻璃高脚红酒杯网红粉色蝴蝶玻璃酒杯批发</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>雨落</td>\n", "      <td>22.00</td>\n", "      <td>0</td>\n", "      <td>22.00</td>\n", "      <td>[]</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN01Nfl8...</td>\n", "      <td>NaN</td>\n", "      <td>100</td>\n", "      <td>active</td>\n", "      <td>{'source': 'alibaba', 'productUrl': 'https://d...</td>\n", "      <td>[{'minQuantity': 50, 'maxQuantity': None, 'pri...</td>\n", "      <td>[{'type': '规格尺寸', 'name': '19.5*9.5*8cm', 'val...</td>\n", "      <td>[]</td>\n", "      <td>{'is_imported': '否', '版权': '无', '全国工业生产许可证编号':...</td>\n", "      <td>system</td>\n", "      <td>system</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>成人用品无人售货机选址装修货源全套服务免费加盟无人外卖店</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>双凯</td>\n", "      <td>1000.00</td>\n", "      <td>0</td>\n", "      <td>1000.00</td>\n", "      <td>[]</td>\n", "      <td>[https://img.alicdn.com/imgextra/i2/**********...</td>\n", "      <td>NaN</td>\n", "      <td>100</td>\n", "      <td>active</td>\n", "      <td>{'source': 'alibaba', 'productUrl': 'https://d...</td>\n", "      <td>[{'minQuantity': 1, 'maxQuantity': None, 'pric...</td>\n", "      <td>[]</td>\n", "      <td>[]</td>\n", "      <td>{'是否跨境出口专供货源': '否', 'brand': '双凯', '商品类型': '智能...</td>\n", "      <td>system</td>\n", "      <td>system</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>塑包铝筒灯6寸塑包铝筒灯外壳 LED 筒灯外壳套件厂家直销</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>宇光</td>\n", "      <td>10.00</td>\n", "      <td>0</td>\n", "      <td>10.00</td>\n", "      <td>[]</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/2016/718/2...</td>\n", "      <td>NaN</td>\n", "      <td>100</td>\n", "      <td>active</td>\n", "      <td>{'source': 'alibaba', 'productUrl': 'https://d...</td>\n", "      <td>[{'minQuantity': 1, 'maxQuantity': None, 'pric...</td>\n", "      <td>[{'type': '采购量', 'name': 'default', 'value': '...</td>\n", "      <td>[]</td>\n", "      <td>{'is_imported': '否', '表面处理': '哑光光', '加工工艺': '塑...</td>\n", "      <td>system</td>\n", "      <td>system</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2023春季新款抹胸缩褶开叉缎面礼服裙欧美女装性感修身包臀连衣裙</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>Detail</td>\n", "      <td>38.00</td>\n", "      <td>0</td>\n", "      <td>38.00</td>\n", "      <td>[]</td>\n", "      <td>[https://cbu01.alicdn.com/img/ibank/O1CN01ptTH...</td>\n", "      <td>NaN</td>\n", "      <td>100</td>\n", "      <td>active</td>\n", "      <td>{'source': 'alibaba', 'productUrl': 'https://d...</td>\n", "      <td>[{'minQuantity': 1, 'maxQuantity': None, 'pric...</td>\n", "      <td>[{'type': '尺码', 'name': 'S', 'value': 'S', 'pr...</td>\n", "      <td>[]</td>\n", "      <td>{}</td>\n", "      <td>system</td>\n", "      <td>system</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>双面仿真丝眼罩睡眠航空睡觉遮光眼罩桑蚕丝热敷冰敷冰丝眼罩</td>\n", "      <td></td>\n", "      <td></td>\n", "      <td>无</td>\n", "      <td>2.65</td>\n", "      <td>0</td>\n", "      <td>2.65</td>\n", "      <td>[]</td>\n", "      <td>[https://img.alicdn.com/imgextra/i1/**********...</td>\n", "      <td>NaN</td>\n", "      <td>100</td>\n", "      <td>active</td>\n", "      <td>{'source': 'alibaba', 'productUrl': 'https://d...</td>\n", "      <td>[{'minQuantity': 2, 'maxQuantity': None, 'pric...</td>\n", "      <td>[{'type': 'Ear Plugs &amp; Packaging', 'name': '不加...</td>\n", "      <td>[{'name': '加图加字', 'description': 'Printing', '...</td>\n", "      <td>{'is_imported': '否', 'style': '简约时尚', '功能': '防...</td>\n", "      <td>system</td>\n", "      <td>system</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                              title description curationNotes supplierId  \\\n", "0   蝴蝶结香槟杯ins风情侣玻璃高脚红酒杯网红粉色蝴蝶玻璃酒杯批发                                   雨落   \n", "1      成人用品无人售货机选址装修货源全套服务免费加盟无人外卖店                                   双凯   \n", "2     塑包铝筒灯6寸塑包铝筒灯外壳 LED 筒灯外壳套件厂家直销                                   宇光   \n", "3  2023春季新款抹胸缩褶开叉缎面礼服裙欧美女装性感修身包臀连衣裙                               Detail   \n", "4      双面仿真丝眼罩睡眠航空睡觉遮光眼罩桑蚕丝热敷冰敷冰丝眼罩                                    无   \n", "\n", "   priceInYuan  serviceFee  finalPrice tags  \\\n", "0        22.00           0       22.00   []   \n", "1      1000.00           0     1000.00   []   \n", "2        10.00           0       10.00   []   \n", "3        38.00           0       38.00   []   \n", "4         2.65           0        2.65   []   \n", "\n", "                                              images  imageEmbedding  \\\n", "0  [https://cbu01.alicdn.com/img/ibank/O1CN01Nfl8...             NaN   \n", "1  [https://img.alicdn.com/imgextra/i2/**********...             NaN   \n", "2  [https://cbu01.alicdn.com/img/ibank/2016/718/2...             NaN   \n", "3  [https://cbu01.alicdn.com/img/ibank/O1CN01ptTH...             NaN   \n", "4  [https://img.alicdn.com/imgextra/i1/**********...             NaN   \n", "\n", "   stockCount  status                                       providerData  \\\n", "0         100  active  {'source': 'alibaba', 'productUrl': 'https://d...   \n", "1         100  active  {'source': 'alibaba', 'productUrl': 'https://d...   \n", "2         100  active  {'source': 'alibaba', 'productUrl': 'https://d...   \n", "3         100  active  {'source': 'alibaba', 'productUrl': 'https://d...   \n", "4         100  active  {'source': 'alibaba', 'productUrl': 'https://d...   \n", "\n", "                                        pricingTiers  \\\n", "0  [{'minQuantity': 50, 'maxQuantity': None, 'pri...   \n", "1  [{'minQuantity': 1, 'maxQuantity': None, 'pric...   \n", "2  [{'minQuantity': 1, 'maxQuantity': None, 'pric...   \n", "3  [{'minQuantity': 1, 'maxQuantity': None, 'pric...   \n", "4  [{'minQuantity': 2, 'maxQuantity': None, 'pric...   \n", "\n", "                                            variants  \\\n", "0  [{'type': '规格尺寸', 'name': '19.5*9.5*8cm', 'val...   \n", "1                                                 []   \n", "2  [{'type': '采购量', 'name': 'default', 'value': '...   \n", "3  [{'type': '尺码', 'name': 'S', 'value': 'S', 'pr...   \n", "4  [{'type': 'Ear Plugs & Packaging', 'name': '不加...   \n", "\n", "                                      customServices  \\\n", "0                                                 []   \n", "1                                                 []   \n", "2                                                 []   \n", "3                                                 []   \n", "4  [{'name': '加图加字', 'description': 'Printing', '...   \n", "\n", "                                          attributes created<PERSON><PERSON> updated<PERSON>y  \n", "0  {'is_imported': '否', '版权': '无', '全国工业生产许可证编号':...    system    system  \n", "1  {'是否跨境出口专供货源': '否', 'brand': '双凯', '商品类型': '智能...    system    system  \n", "2  {'is_imported': '否', '表面处理': '哑光光', '加工工艺': '塑...    system    system  \n", "3                                                 {}    system    system  \n", "4  {'is_imported': '否', 'style': '简约时尚', '功能': '防...    system    system  "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["products.head()"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>platformUrl</th>\n", "      <th>name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>https://detail.1688.com/offer/838063864671.html</td>\n", "      <td>雨落</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>https://detail.1688.com/offer/837771232054.html</td>\n", "      <td>双凯</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>https://detail.1688.com/offer/529105978270.html</td>\n", "      <td>宇光</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>https://detail.1688.com/offer/710881717490.html</td>\n", "      <td>Detail</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>https://detail.1688.com/offer/809432080362.html</td>\n", "      <td>无</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                       platformUrl    name\n", "0  https://detail.1688.com/offer/838063864671.html      雨落\n", "1  https://detail.1688.com/offer/837771232054.html      双凯\n", "2  https://detail.1688.com/offer/529105978270.html      宇光\n", "3  https://detail.1688.com/offer/710881717490.html  Detail\n", "4  https://detail.1688.com/offer/809432080362.html       无"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["suppliers = pd.read_json('suppliers_to_create.json', orient='records')\n", "suppliers.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "firebase", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 4}