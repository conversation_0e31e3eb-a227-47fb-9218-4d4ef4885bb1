import { v } from "convex/values";
import { internalMutation, query } from "./_generated/server";
import { internal } from "./_generated/api";

// Query to get all unique tags
export const get = query({
  handler: async (ctx) => {
    const tags = await ctx.db.query("uniqueTags").collect();
    return tags.map((t) => t.tag);
  },
});

// Internal mutation to add new tags to the collection
export const addTags = internalMutation({
  args: {
    tags: v.array(v.string()),
  },
  handler: async (ctx, { tags }) => {
    if (tags.length === 0) {
      return;
    }

    // Fetch existing tags to avoid duplicates
    const existingTags = new Set(
      (await ctx.db.query("uniqueTags").collect()).map((t) => t.tag)
    );

    const newTags = tags.filter((tag) => !existingTags.has(tag));

    // Insert only the new tags
    for (const tag of newTags) {
      // Use a try-catch to handle potential race conditions if two mutations run simultaneously
      try {
        await ctx.db.insert("uniqueTags", { tag });
      } catch (error) {
        // If the tag already exists due to a race condition, we can ignore the error
        console.warn(`Could not insert tag, it might already exist: ${tag}`, error);
      }
    }
  },
});
