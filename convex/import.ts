// convex/import.ts
import { mutation } from "./_generated/server";
import { v } from "convex/values";
import { Infer } from "convex/values";
import { getAuthUserId } from "@convex-dev/auth/server";

// Define a validator for the product structure from the JSON file
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const productSchema = v.object({
  title: v.string(),
  description: v.string(),
  curationNotes: v.string(),
  supplierId: v.string(), // This is the supplier name, will be resolved to an Id
  priceInYuan: v.number(),
  serviceFee: v.number(),
  finalPrice: v.number(),
  tags: v.array(v.string()),
  images: v.array(v.string()),
  imageEmbedding: v.optional(v.null()),
  stockCount: v.number(),
  status: v.union(v.literal("active"), v.literal("inactive")),
  providerData: v.object({
    source: v.string(),
    productUrl: v.string(),
    providerId: v.string(),
    lastScraped: v.number(),
    providerSpecificData: v.optional(v.any()),
  }),
  pricingTiers: v.array(v.object({
    minQuantity: v.union(v.number(), v.string(), v.null()),
    maxQuantity: v.optional(v.union(v.number(), v.string(), v.null())),
    price: v.union(v.string(), v.number()),
    currency: v.string(),
    discountPercentage: v.optional(v.union(v.number(), v.string(), v.null())),
  })),
  variants: v.array(v.object({
    type: v.string(),
    name: v.union(v.string(), v.null()),
    value: v.union(v.string(), v.null()),
    priceType: v.string(),
    absolutePrice: v.optional(v.union(v.number(), v.string(), v.null())),
    priceModifier: v.optional(v.union(v.number(), v.string(), v.null())),
    currency: v.optional(v.union(v.string(), v.null())),
    availableQuantity: v.optional(v.union(v.number(), v.string(), v.null())),
    images: v.array(v.string()),
  })),
  customServices: v.array(v.object({
    name: v.string(),
    description: v.string(),
    minQuantity: v.union(v.number(), v.null()),
    price: v.union(v.number(), v.null()),
    currency: v.string(),
    isRequired: v.boolean(),
  })),
  attributes: v.array(v.object({ name: v.string(), value: v.string() })),
  createdBy: v.string(),
  updatedBy: v.string(),
});

export type ProductJson = Infer<typeof productSchema>;

export const importProducts = mutation({
  args: {
    products: v.array(productSchema),
  },
  handler: async (ctx, { products }) => {
    const userId = await getAuthUserId(ctx);;
    if (!userId) {
      throw new Error("You must be logged in to import products.");
    }

    const adminUser = await ctx.db.query("adminUsers").withIndex("by_user", (q) => q.eq("userId", userId)).first();

    if (!adminUser || !adminUser.isActive) {
      throw new Error("You must be an admin to import products.");
    }

    if (!Array.isArray(products)) {
      throw new Error("Invalid JSON file: Expected an array of products.");
    }

    // Define transformProduct inside the handler
    const transformProduct = (product: ProductJson) => {
      // attributes is already transformed into array in page.tsx, so use as is
      const attributes = product.attributes || [];

      const customServices = (product.customServices || []).map((service) => ({
        ...service,
        price: service.price === null ? 0 : service.price,
        minQuantity: service.minQuantity === null ? 0 : service.minQuantity,
      }));

      const pricingTiers = (product.pricingTiers || []).map((tier) => ({
        ...tier,
        minQuantity: tier.minQuantity === null ? 1 : (typeof tier.minQuantity === 'string' ? parseFloat(tier.minQuantity) || 1 : tier.minQuantity),
        maxQuantity: tier.maxQuantity === null ? undefined : (typeof tier.maxQuantity === 'string' ? parseFloat(tier.maxQuantity) : tier.maxQuantity),
        price: typeof tier.price === 'string' ? parseFloat(tier.price) : tier.price,
        discountPercentage: tier.discountPercentage === null ? undefined : (typeof tier.discountPercentage === 'string' ? parseFloat(tier.discountPercentage) : tier.discountPercentage),
      }));

      const variants = (product.variants || []).map((variant) => ({
        ...variant,
        name: variant.name === null ? 'Default' : variant.name,
        value: variant.value === null ? 'Default' : variant.value,
        absolutePrice: variant.absolutePrice === null ? undefined : (typeof variant.absolutePrice === 'string' ? parseFloat(variant.absolutePrice) : variant.absolutePrice),
        priceModifier: variant.priceModifier === null ? undefined : (typeof variant.priceModifier === 'string' ? parseFloat(variant.priceModifier) : variant.priceModifier),
        availableQuantity: variant.availableQuantity === null ? undefined : (typeof variant.availableQuantity === 'string' ? parseInt(variant.availableQuantity) : variant.availableQuantity),
        priceType: variant.priceType as "modifier" | "absolute",
        currency: variant.currency === null ? undefined : variant.currency,
      }));

      return {
        ...product,
        attributes,
        customServices,
        pricingTiers,
        variants,
      };
    };

    for (const product of products) {
      // Check if product with the same URL already exists
      const existingProduct = await ctx.db
        .query("products")
        .withIndex("by_provider_product_url", (q) =>
          q.eq("providerData.productUrl", product.providerData.productUrl)
        )
        .first();

      if (existingProduct) {
        console.log(`Product with URL ${product.providerData.productUrl} already exists. Skipping.`);
        continue;
      }

      // 1. Find or create the supplier
      let supplierId;
      const supplierName = product.supplierId;

      const existingSupplier = await ctx.db
        .query("suppliers")
        .filter((q) => q.eq(q.field("name"), supplierName))
        .first();

      if (existingSupplier) {
        supplierId = existingSupplier._id;
      } else {
        supplierId = await ctx.db.insert("suppliers", {
          name: supplierName,
          isActive: true,
          createdBy: userId,
          contactInfo: {},
        });
      }

      // 2. Transform the product
      const transformedProduct = transformProduct(product);

      // 3. Prepare the product for insertion
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { supplierId: _, ...productData } = transformedProduct;

      // 4. Insert the product
      await ctx.db.insert("products", {
        ...productData,
        supplierId,
        createdBy: userId,
        updatedBy: userId,
        status: "inactive",
      });
    }

    return { count: products.length };
  },
});

