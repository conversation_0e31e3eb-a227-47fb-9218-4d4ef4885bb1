import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { requirePermission } from "./auth/rbac";
import { PERMISSIONS } from "./auth/permissions";
import { getAuthUserId } from "@convex-dev/auth/server";
import { groupBuyStatsAggregate } from "./aggregates";
import { Doc, Id } from "./_generated/dataModel";

// PERFORMANCE OPTIMIZATION: Batch fetch helper functions
async function batchFetchProducts(ctx: any, productIds: Id<"products">[]) {
  const products = await Promise.all(productIds.map(id => ctx.db.get(id)));
  const productsMap = new Map<Id<"products">, Doc<"products"> | null>();
  productIds.forEach((id, index) => {
    productsMap.set(id, products[index]);
  });
  return productsMap;
}

async function batchFetchUsers(ctx: any, userIds: Id<"users">[]) {
  const users = await Promise.all(userIds.map(id => ctx.db.get(id)));
  const usersMap = new Map<Id<"users">, Doc<"users"> | null>();
  userIds.forEach((id, index) => {
    usersMap.set(id, users[index]);
  });
  return usersMap;
}

// Query to get all group buys with filtering
export const getGroupBuys = query({
  args: {
    status: v.optional(v.union(v.literal("active"), v.literal("completed"), v.literal("expired"))),
    productId: v.optional(v.id("products")),
  },
  handler: async (ctx, { status, productId }) => {
    await requirePermission(ctx, PERMISSIONS.GROUP_BUYS_VIEW);

    // Build query with proper initialization
    let query;

    // Apply filters
    if (status) {
      query = ctx.db.query("groupBuys").withIndex("by_status", (q) => q.eq("status", status));
    } else if (productId) {
      query = ctx.db.query("groupBuys").withIndex("by_product", (q) => q.eq("productId", productId));
    } else {
      query = ctx.db.query("groupBuys");
    }

    const groupBuys = await query.collect();

    // PERFORMANCE OPTIMIZATION: Batch fetch all related data
    const productIds = new Set(groupBuys.map(gb => gb.productId));
    const userIds = new Set(groupBuys.map(gb => gb.createdBy));

    // Batch fetch products and users in parallel
    const [productsMap, usersMap] = await Promise.all([
      batchFetchProducts(ctx, Array.from(productIds)),
      batchFetchUsers(ctx, Array.from(userIds))
    ]);

    // Enrich group buys with fetched data
    const enrichedGroupBuys = groupBuys.map((groupBuy) => {
      const product = productsMap.get(groupBuy.productId);
      const createdBy = usersMap.get(groupBuy.createdBy);

      // Calculate current tier and price
      const currentTier = groupBuy.targetTiers
        .filter(tier => groupBuy.currentParticipants >= tier.quantity)
        .sort((a, b) => b.quantity - a.quantity)[0];

      const nextTier = groupBuy.targetTiers
        .filter(tier => groupBuy.currentParticipants < tier.quantity)
        .sort((a, b) => a.quantity - b.quantity)[0];

      return {
        ...groupBuy,
        product: product ? {
          title: product.title,
          images: product.images,
          finalPrice: product.finalPrice,
          status: product.status,
        } : null,
        createdByUser: createdBy ? {
          name: createdBy.name,
          email: createdBy.email,
        } : null,
        currentTier,
        nextTier,
        progress: currentTier ?
          Math.min(100, (groupBuy.currentParticipants / Math.max(...groupBuy.targetTiers.map(t => t.quantity))) * 100) :
          0,
      };
    });

    return enrichedGroupBuys;
  },
});

// Query to get a single group buy by ID
export const getGroupBuy = query({
  args: { id: v.id("groupBuys") },
  handler: async (ctx, { id }) => {
    await requirePermission(ctx, PERMISSIONS.GROUP_BUYS_VIEW);

    const groupBuy = await ctx.db.get(id);
    if (!groupBuy) {
      throw new Error("Group buy not found");
    }

    const product = await ctx.db.get(groupBuy.productId);
    const createdBy = await ctx.db.get(groupBuy.createdBy);

    // Calculate tier information
    const currentTier = groupBuy.targetTiers
      .filter(tier => groupBuy.currentParticipants >= tier.quantity)
      .sort((a, b) => b.quantity - a.quantity)[0];

    const nextTier = groupBuy.targetTiers
      .filter(tier => groupBuy.currentParticipants < tier.quantity)
      .sort((a, b) => a.quantity - b.quantity)[0];

    const maxTier = groupBuy.targetTiers
      .sort((a, b) => b.quantity - a.quantity)[0];

    return {
      ...groupBuy,
      product: product ? {
        ...product,
      } : null,
      createdByUser: createdBy ? {
        name: createdBy.name,
        email: createdBy.email,
      } : null,
      currentTier,
      nextTier,
      maxTier,
      progress: Math.min(100, (groupBuy.currentParticipants / maxTier.quantity) * 100),
      timeRemaining: Math.max(0, groupBuy.endTime - Date.now()),
    };
  },
});

// Mutation to create a new group buy
export const createGroupBuy = mutation({
  args: {
    productId: v.id("products"),
    targetTiers: v.array(v.object({
      quantity: v.number(),
      price: v.number(),
    })),
    endTime: v.number(),
    providerPricingTiers: v.array(v.object({
      minParticipants: v.number(),
      pricePerUnit: v.number(),
      ourCommission: v.number(),
      finalPrice: v.number(),
    })),
  },
  handler: async (ctx, args) => {
    await requirePermission(ctx, PERMISSIONS.GROUP_BUYS_CREATE);

    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Validate product exists and is active
    const product = await ctx.db.get(args.productId);
    if (!product) {
      throw new Error("Product not found");
    }

    if (product.status !== "active") {
      throw new Error("Cannot create group buy for inactive product");
    }

    // Validate target tiers
    if (args.targetTiers.length === 0) {
      throw new Error("At least one target tier is required");
    }

    // Validate tier quantities are positive and unique
    const quantities = args.targetTiers.map(t => t.quantity);
    if (quantities.some(q => q <= 0)) {
      throw new Error("Tier quantities must be positive");
    }

    if (new Set(quantities).size !== quantities.length) {
      throw new Error("Tier quantities must be unique");
    }

    // Validate tier prices are positive
    if (args.targetTiers.some(t => t.price <= 0)) {
      throw new Error("Tier prices must be positive");
    }

    // Validate end time is in the future
    if (args.endTime <= Date.now()) {
      throw new Error("End time must be in the future");
    }

    // Check if there's already an active group buy for this product
    const existingGroupBuy = await ctx.db
      .query("groupBuys")
      .withIndex("by_product", (q) => q.eq("productId", args.productId))
      .filter((q) => q.eq(q.field("status"), "active"))
      .first();

    if (existingGroupBuy) {
      throw new Error("There is already an active group buy for this product");
    }

    // Sort tiers by quantity for consistency
    const sortedTiers = args.targetTiers.sort((a, b) => a.quantity - b.quantity);

    const groupBuyId = await ctx.db.insert("groupBuys", {
      productId: args.productId,
      targetTiers: sortedTiers,
      currentParticipants: 0,
      status: "active",
      startTime: Date.now(),
      endTime: args.endTime,
      createdBy: userId,
      providerPricingTiers: args.providerPricingTiers,
    });

    // Update aggregates
    const groupBuy = await ctx.db.get(groupBuyId);
    if (groupBuy) {
      await groupBuyStatsAggregate.insert(ctx, groupBuy);
    }

    return groupBuyId;
  },
});

// Mutation for a user to join a group buy
export const joinGroupBuy = mutation({
  args: {
    groupBuyId: v.id("groupBuys"),
    quantity: v.number(),
  },
  handler: async (ctx, { groupBuyId, quantity }) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const groupBuy = await ctx.db.get(groupBuyId);
    if (!groupBuy) {
      throw new Error("Group buy not found");
    }

    if (groupBuy.status !== "active") {
      throw new Error("This group buy is not active");
    }

    if (groupBuy.endTime <= Date.now()) {
      throw new Error("This group buy has ended");
    }

    if (quantity <= 0) {
      throw new Error("Quantity must be positive");
    }

    // Check if user has already joined
    const existingParticipation = await ctx.db
      .query("groupBuyParticipants")
      .withIndex("by_group_buy_and_user", (q) =>
        q.eq("groupBuyId", groupBuyId).eq("userId", userId)
      )
      .first();

    if (existingParticipation) {
      // Update existing participation
      await ctx.db.patch(existingParticipation._id, {
        quantity: existingParticipation.quantity + quantity,
      });
    } else {
      // Create new participation record
      await ctx.db.insert("groupBuyParticipants", {
        groupBuyId,
        userId,
        quantity,
        joinedAt: Date.now(),
      });
    }

    // Update the total participants count in the groupBuys table
    const participations = await ctx.db
      .query("groupBuyParticipants")
      .withIndex("by_group_buy", (q) => q.eq("groupBuyId", groupBuyId))
      .collect();

    const totalParticipants = participations.reduce((sum, p) => sum + p.quantity, 0);

    const oldGroupBuy = await ctx.db.get(groupBuyId);
    await ctx.db.patch(groupBuyId, {
      currentParticipants: totalParticipants,
    });

    // Update aggregates
    const updatedGroupBuy = await ctx.db.get(groupBuyId);
    if (oldGroupBuy && updatedGroupBuy) {
      await groupBuyStatsAggregate.replace(ctx, oldGroupBuy, updatedGroupBuy);
    }

    return { success: true };
  },
});

// Mutation to end a group buy (manually or automatically)
export const endGroupBuy = mutation({
  args: {
    id: v.id("groupBuys"),
    reason: v.optional(v.string()),
  },
  handler: async (ctx, { id, reason }) => {
    await requirePermission(ctx, PERMISSIONS.GROUP_BUYS_EDIT);

    const groupBuy = await ctx.db.get(id);
    if (!groupBuy) {
      throw new Error("Group buy not found");
    }

    if (groupBuy.status !== "active") {
      throw new Error("Group buy is not active");
    }

    // Determine if the group buy was successful
    const hasMetMinimumTier = groupBuy.targetTiers.some(
      tier => groupBuy.currentParticipants >= tier.quantity
    );

    const newStatus = hasMetMinimumTier ? "completed" : "expired";

    await ctx.db.patch(id, {
      status: newStatus,
    });

    // Update aggregates
    const updatedGroupBuy = await ctx.db.get(id);
    if (updatedGroupBuy) {
      await groupBuyStatsAggregate.replace(ctx, groupBuy, updatedGroupBuy);
    }

    return {
      success: true,
      status: newStatus,
      participantCount: groupBuy.currentParticipants,
      reason
    };
  },
});

// Query to get active group buys that need attention (ending soon or completed)
export const getGroupBuysNeedingAttention = query({
  args: {},
  handler: async (ctx) => {
    await requirePermission(ctx, PERMISSIONS.GROUP_BUYS_VIEW);

    const now = Date.now();
    const oneDayFromNow = now + (24 * 60 * 60 * 1000);

    const groupBuys = await ctx.db
      .query("groupBuys")
      .withIndex("by_status", (q) => q.eq("status", "active"))
      .collect();

    const needingAttention = [];

    for (const groupBuy of groupBuys) {
      const product = await ctx.db.get(groupBuy.productId);
      
      // Check if ending soon (within 24 hours)
      const endingSoon = groupBuy.endTime <= oneDayFromNow;
      
      // Check if has met minimum tier
      const hasMetMinimumTier = groupBuy.targetTiers.some(
        tier => groupBuy.currentParticipants >= tier.quantity
      );

      // Check if expired
      const isExpired = groupBuy.endTime <= now;

      if (endingSoon || hasMetMinimumTier || isExpired) {
        needingAttention.push({
          ...groupBuy,
          product: product ? {
            title: product.title,
            images: product.images,
          } : null,
          endingSoon,
          hasMetMinimumTier,
          isExpired,
          timeRemaining: Math.max(0, groupBuy.endTime - now),
        });
      }
    }

    return needingAttention;
  },
});

// Query to get group buy statistics - PERFORMANCE OPTIMIZED
export const getGroupBuyStats = query({
  args: {},
  handler: async (ctx) => {
    await requirePermission(ctx, PERMISSIONS.ANALYTICS_VIEW);

    // PERFORMANCE OPTIMIZATION: Use aggregates for O(log n) performance
    const [total, active, completed, expired, totalParticipants] = await Promise.all([
      groupBuyStatsAggregate.count(ctx),
      groupBuyStatsAggregate.count(ctx, {
        bounds: { lower: { key: "active", inclusive: true }, upper: { key: "active", inclusive: true } }
      }),
      groupBuyStatsAggregate.count(ctx, {
        bounds: { lower: { key: "completed", inclusive: true }, upper: { key: "completed", inclusive: true } }
      }),
      groupBuyStatsAggregate.count(ctx, {
        bounds: { lower: { key: "expired", inclusive: true }, upper: { key: "expired", inclusive: true } }
      }),
      groupBuyStatsAggregate.sum(ctx) // Sum of all participants
    ]);

    const averageParticipants = total > 0 ? Math.round(totalParticipants / total) : 0;
    const successRate = total > 0 ? Math.round((completed / total) * 100) : 0;

    return {
      total,
      active,
      completed,
      expired,
      totalParticipants,
      averageParticipants,
      successRate,
    };
  },
});
