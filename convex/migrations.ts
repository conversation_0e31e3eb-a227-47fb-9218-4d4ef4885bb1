import { internalMutation, MutationCtx } from "./_generated/server";
import { productStatsAggregate, productStockAggregate, orderStatsAggregate, orderCountAggregate, supplierStatsAggregate } from "./aggregates";

// Helper for product aggregates backfill
async function _backfillProductAggregates(ctx: MutationCtx) {
    console.log("Starting product aggregates backfill...");
    
    // Clear existing aggregates to start fresh
    await productStatsAggregate.clear(ctx);
    await productStockAggregate.clear(ctx);
    
    // Get all products and add them to aggregates
    const products = await ctx.db.query("products").collect();
    
    for (const product of products) {
      await productStatsAggregate.insertIfDoesNotExist(ctx, product);
      await productStockAggregate.insertIfDoesNotExist(ctx, product);
    }
    
    console.log(`Backfilled ${products.length} products to aggregates`);
    return { success: true, count: products.length };
}

// Migration to backfill product aggregates
export const backfillProductAggregates = internalMutation({
  args: {},
  handler: async (ctx) => {
    return await _backfillProductAggregates(ctx);
  },
});

// Helper for order aggregates backfill
async function _backfillOrderAggregates(ctx: MutationCtx) {
    console.log("Starting order aggregates backfill...");
    
    // Clear existing aggregates to start fresh
    await orderStatsAggregate.clear(ctx);
    await orderCountAggregate.clear(ctx);
    
    // Get all orders and add them to aggregates
    const orders = await ctx.db.query("orders").collect();
    
    for (const order of orders) {
      await orderStatsAggregate.insertIfDoesNotExist(ctx, order);
      await orderCountAggregate.insertIfDoesNotExist(ctx, order);
    }
    
    console.log(`Backfilled ${orders.length} orders to aggregates`);
    return { success: true, count: orders.length };
}

// Migration to backfill order aggregates
export const backfillOrderAggregates = internalMutation({
  args: {},
  handler: async (ctx) => {
    return await _backfillOrderAggregates(ctx);
  },
});

// Helper for supplier aggregates backfill
async function _backfillSupplierAggregates(ctx: MutationCtx) {
    console.log("Starting supplier aggregates backfill...");

    // Clear existing aggregates to start fresh
    await supplierStatsAggregate.clear(ctx);

    // Get all suppliers and add them to aggregates
    const suppliers = await ctx.db.query("suppliers").collect();

    for (const supplier of suppliers) {
      await supplierStatsAggregate.insertIfDoesNotExist(ctx, supplier);
    }

    console.log(`Backfilled ${suppliers.length} suppliers to aggregates`);
    return { success: true, count: suppliers.length };
}

// Migration to backfill supplier aggregates
export const backfillSupplierAggregates = internalMutation({
  args: {},
  handler: async (ctx) => {
    return await _backfillSupplierAggregates(ctx);
  },
});

// Combined migration to backfill all aggregates
export const backfillAllAggregates = internalMutation({
  args: {},
  handler: async (ctx) => {
    console.log("Starting full aggregates backfill...");

    const productResult = await _backfillProductAggregates(ctx);
    const orderResult = await _backfillOrderAggregates(ctx);
    const supplierResult = await _backfillSupplierAggregates(ctx);

    console.log("Completed full aggregates backfill");
    return {
      success: true,
      products: productResult.count,
      orders: orderResult.count,
      suppliers: supplierResult.count,
    };
  },
});
