{"name": "ma<PERSON>o", "version": "0.1.0", "private": true, "scripts": {"dev": "npm-run-all --parallel dev:frontend dev:backend", "dev:frontend": "next dev", "dev:backend": "convex dev", "predev": "convex dev --until-success && convex dev --once --run-sh \"node setup.mjs --once\" && convex dashboard", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:security": "jest __tests__/security", "setup-admin": "node scripts/make-super-admin.js"}, "dependencies": {"@convex-dev/aggregate": "^0.1.23", "@convex-dev/auth": "^0.0.81", "@google-cloud/aiplatform": "^5.6.0", "@google-cloud/vertexai": "^1.10.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@tanstack/react-table": "^8.21.3", "@tanstack/react-virtual": "^3.13.12", "@upstash/ratelimit": "^2.0.6", "@upstash/redis": "^1.35.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.26.1", "date-fns": "^4.1.0", "fuse.js": "^7.1.0", "jose": "^6.0.13", "lucide-react": "^0.541.0", "next": "15.2.3", "next-themes": "^0.4.6", "react": "^19.0.0", "react-day-picker": "^9.9.0", "react-dom": "^19.0.0", "react-window": "^2.0.2", "recharts": "^3.1.2", "stream-json": "^1.9.1", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20.19.11", "@types/react": "^19", "@types/react-dom": "^19", "dotenv": "^16.4.7", "eslint": "^9", "eslint-config-next": "15.2.3", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "npm-run-all": "^4.1.5", "prettier": "^3.5.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.7", "typescript": "^5"}}