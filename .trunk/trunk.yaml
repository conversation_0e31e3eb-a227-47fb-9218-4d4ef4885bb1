# This file controls the behavior of Trunk: https://docs.trunk.io/cli
# To learn more about the format of this file, see https://docs.trunk.io/reference/trunk-yaml
version: 0.1
cli:
  version: 1.25.0
# Trunk provides extensibility via plugins. (https://docs.trunk.io/plugins)
plugins:
  sources:
    - id: trunk
      ref: v1.7.2
      uri: https://github.com/trunk-io/plugins
# Many linters and tools depend on runtimes - configure them here. (https://docs.trunk.io/runtimes)
runtimes:
  enabled:
    - node@22.16.0
    - python@3.10.8
# This is the section where you manage your linters. (https://docs.trunk.io/check/configuration)
lint:
  enabled:
    - bandit@1.8.6
    - black@25.1.0
    - checkov@3.2.467
    - eslint@9.34.0
    - git-diff-check
    - isort@6.0.1
    - markdownlint@0.45.0
    - prettier@3.6.2
    - ruff@0.12.11
    - svgo@4.0.0
    - trufflehog@3.90.5
actions:
  disabled:
    - trunk-announce
    - trunk-check-pre-push
    - trunk-fmt-pre-commit
  enabled:
    - trunk-upgrade-available
