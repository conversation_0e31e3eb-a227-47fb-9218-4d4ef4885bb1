#!/usr/bin/env python3
"""
Product Data Splitting Script

This script splits a large processed_products.json file into smaller chunks
containing a maximum number of products per file. This enables the web app
to process data in manageable batches without memory issues.

Usage:
    python split_products.py [input_file] [output_dir] [--chunk-size N]

Arguments:
    input_file: Path to the processed_products.json file (default: ./export/processed_products.json)
    output_dir: Directory to save chunk files (default: ./export/product_chunks/)
    --chunk-size: Maximum products per chunk (default: 1000)

Output:
    - Creates product_chunks/ directory with chunk files
    - Files named as products_chunk_001.json, products_chunk_002.json, etc.
    - Saves splitting report
"""

import json
import os
import argparse
from typing import List, Dict, Any
from pathlib import Path
import logging
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProductDataSplitter:
    """Handles splitting of product data into manageable chunks."""

    def __init__(self, input_file: str, output_dir: str, chunk_size: int = 100):
        self.input_file = Path(input_file)
        self.output_dir = Path(output_dir)
        self.chunk_size = chunk_size
        self.chunks_created = 0
        self.total_products = 0

    def validate_input(self) -> None:
        """Validate input file exists and is readable."""
        if not self.input_file.exists():
            raise FileNotFoundError(f"Input file not found: {self.input_file}")

        if not self.input_file.is_file():
            raise ValueError(f"Input path is not a file: {self.input_file}")

        logger.info(f"Input file validated: {self.input_file}")

    def create_output_directory(self) -> None:
        """Create output directory if it doesn't exist."""
        self.output_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"Output directory ready: {self.output_dir}")

    def load_products(self) -> List[Dict[str, Any]]:
        """Load products from the input JSON file."""
        logger.info(f"Loading products from {self.input_file}")

        with open(self.input_file, 'r', encoding='utf-8') as f:
            products = json.load(f)

        if not isinstance(products, list):
            raise ValueError("Input JSON must contain an array of products")

        self.total_products = len(products)
        logger.info(f"Loaded {self.total_products} products")
        return products

    def split_into_chunks(self, products: List[Dict[str, Any]]) -> None:
        """Split products into chunks and save each chunk."""
        logger.info(f"Splitting into chunks of {self.chunk_size} products each")

        for i in range(0, len(products), self.chunk_size):
            chunk = products[i:i + self.chunk_size]
            chunk_number = (i // self.chunk_size) + 1
            self.save_chunk(chunk, chunk_number)

        logger.info(f"Created {self.chunks_created} chunk files")

    def save_chunk(self, chunk: List[Dict[str, Any]], chunk_number: int) -> None:
        """Save a single chunk to a JSON file."""
        chunk_filename = f"products_chunk_{chunk_number:03d}.json"
        chunk_path = self.output_dir / chunk_filename

        with open(chunk_path, 'w', encoding='utf-8') as f:
            json.dump(chunk, f, indent=2, ensure_ascii=False)

        self.chunks_created += 1
        logger.info(f"Saved chunk {chunk_number}: {len(chunk)} products -> {chunk_filename}")

    def save_splitting_report(self) -> None:
        """Save a report of the splitting operation."""
        report = {
            "operation_timestamp": datetime.now().isoformat(),
            "input_file": str(self.input_file),
            "output_directory": str(self.output_dir),
            "chunk_size": self.chunk_size,
            "total_products": self.total_products,
            "chunks_created": self.chunks_created,
            "average_products_per_chunk": self.total_products / self.chunks_created if self.chunks_created > 0 else 0,
            "chunk_files": [f"products_chunk_{i+1:03d}.json" for i in range(self.chunks_created)]
        }

        report_path = self.output_dir / "splitting_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        logger.info(f"Splitting report saved: {report_path}")

    def run(self) -> None:
        """Execute the complete splitting process."""
        try:
            logger.info("="*60)
            logger.info("PRODUCT DATA SPLITTING STARTED")
            logger.info("="*60)

            self.validate_input()
            self.create_output_directory()
            products = self.load_products()
            self.split_into_chunks(products)
            self.save_splitting_report()

            logger.info("="*60)
            logger.info("PRODUCT DATA SPLITTING COMPLETED")
            logger.info(f"Total products: {self.total_products}")
            logger.info(f"Chunks created: {self.chunks_created}")
            logger.info(".1f")
            logger.info("="*60)

        except Exception as e:
            logger.error(f"Splitting failed: {e}")
            raise

def main():
    """Main entry point with argument parsing."""
    parser = argparse.ArgumentParser(description="Split product data into manageable chunks")
    parser.add_argument(
        "input_file",
        nargs="?",
        default="./export/processed_products.json",
        help="Path to input JSON file (default: ./export/processed_products.json)"
    )
    parser.add_argument(
        "output_dir",
        nargs="?",
        default="./export/product_chunks",
        help="Output directory for chunks (default: ./export/product_chunks)"
    )
    parser.add_argument(
        "--chunk-size",
        type=int,
        default=100,
        help="Maximum products per chunk (default: 100)"
    )

    args = parser.parse_args()

    splitter = ProductDataSplitter(args.input_file, args.output_dir, args.chunk_size)
    splitter.run()

if __name__ == "__main__":
    main()