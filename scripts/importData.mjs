import fs from 'fs';
import path from 'path';
import { ConvexHttpClient } from 'convex/browser';
import jsonStream from 'stream-json';
const { parser } = jsonStream;
import arrayStreamer from 'stream-json/streamers/StreamArray.js';
const { streamArray } = arrayStreamer;
import dotenv from 'dotenv';

dotenv.config();

// --- Configuration ---
const BATCH_SIZE = 100; // Number of products to import at a time
const CONVEX_URL = process.env.NEXT_PUBLIC_CONVEX_URL;
const DATA_FILE_PATH = path.join(process.cwd(), 'export', 'processed_products.json');
const SKIP_COUNT = 103920; // Number of products to skip from the beginning of the file
// -------------------

async function main() {
  if (!CONVEX_URL) {
    console.error('Error: CONVEX_URL environment variable not set.');
    console.error("Please set it to your project's deployment URL.");
    process.exit(1);
  }

  console.log(`Connecting to Convex at ${CONVEX_URL}...`);
  const client = new ConvexHttpClient(CONVEX_URL);

  console.log(`Streaming data from ${DATA_FILE_PATH}...`);
  if (SKIP_COUNT > 0) {
    console.log(`Skipping the first ${SKIP_COUNT} products...`);
  }

  let productsBatch = [];
  let totalProductsProcessed = 0;
  let batchCounter = 0;
  let itemsStreamed = 0;

  const processBatch = async (batch) => {
    batchCounter++;
    console.log(`--- Processing Batch ${batchCounter} ---`);
    const promises = batch.map((product, index) => {
      const productNumber = (batchCounter - 1) * BATCH_SIZE + index + 1 + SKIP_COUNT;
      return client.mutation('import:importProduct', { product })
        .then(() => {
          console.log(`  (${productNumber}) Successfully imported product: ${product.title}`);
        })
        .catch((error) => {
          console.error(`  (${productNumber}) Failed to import product: ${product.title}`);
          console.error(`    Error: ${error.message}`);
        });
    });

    await Promise.all(promises);
    console.log(`--- Batch ${batchCounter} complete ---
`);
  };

  const fileStream = fs.createReadStream(DATA_FILE_PATH);
  const jsonStream = fileStream.pipe(parser()).pipe(streamArray());

  // Transform product data to handle Chinese characters and null values
  const transformProduct = (product) => {
    // Transform attributes from object to array
    const attributes = Object.entries(product.attributes || {}).map(([name, value]) => ({
      name,
      value,
    }));

    // Fix null values in customServices
    const customServices = (product.customServices || []).map(service => ({
      ...service,
      price: service.price === null ? 0 : service.price,
      minQuantity: service.minQuantity === null ? 0 : service.minQuantity,
    }));

    // Fix null/string values in pricingTiers
    const pricingTiers = (product.pricingTiers || []).map(tier => ({
      ...tier,
      minQuantity: tier.minQuantity === null ? 1 : (typeof tier.minQuantity === 'string' ? parseFloat(tier.minQuantity) || 1 : tier.minQuantity),
      maxQuantity: tier.maxQuantity === null ? undefined : (typeof tier.maxQuantity === 'string' ? parseFloat(tier.maxQuantity) : tier.maxQuantity),
      price: typeof tier.price === 'string' ? parseFloat(tier.price) : tier.price,
      discountPercentage: tier.discountPercentage === null ? undefined : (typeof tier.discountPercentage === 'string' ? parseFloat(tier.discountPercentage) : tier.discountPercentage),
    }));

    // Fix null values in variants
    const variants = (product.variants || []).map(variant => ({
      ...variant,
      name: variant.name === null ? 'Default' : variant.name,
      value: variant.value === null ? 'Default' : variant.value,
      absolutePrice: variant.absolutePrice === null ? undefined : (typeof variant.absolutePrice === 'string' ? parseFloat(variant.absolutePrice) : variant.absolutePrice),
      priceModifier: variant.priceModifier === null ? undefined : (typeof variant.priceModifier === 'string' ? parseFloat(variant.priceModifier) : variant.priceModifier),
      availableQuantity: variant.availableQuantity === null ? undefined : (typeof variant.availableQuantity === 'string' ? parseInt(variant.availableQuantity) : variant.availableQuantity),
    }));

    return {
      ...product,
      attributes,
      customServices,
      pricingTiers,
      variants,
    };
  };

  return new Promise((resolve, reject) => {
    jsonStream.on('data', ({ value }) => {
      itemsStreamed++;
      if (itemsStreamed <= SKIP_COUNT) {
        if (itemsStreamed % 10000 === 0) {
          console.log(`... skipped ${itemsStreamed} products`);
        }
        return;
      }

      if (itemsStreamed === SKIP_COUNT + 1) {
        console.log(`Finished skipping. Starting import from product ${itemsStreamed}.`);
      }

      const transformedProduct = transformProduct(value);
      productsBatch.push(transformedProduct);
      totalProductsProcessed++;
      if (productsBatch.length >= BATCH_SIZE) {
        jsonStream.pause(); // Pause the stream to process the batch
        processBatch([...productsBatch]).then(() => {
          productsBatch = []; // Clear the batch
          jsonStream.resume(); // Resume the stream
        }).catch(reject);
      }
    });

    jsonStream.on('end', () => {
      // Process any remaining products in the last batch
      if (productsBatch.length > 0) {
        // Transform the remaining products in the batch
        const transformedBatch = productsBatch.map(transformProduct);
        processBatch(transformedBatch).then(() => {
          console.log(`
Import process finished! Processed ${totalProductsProcessed} products.`);
          resolve();
        }).catch(reject);
      } else {
        console.log(`
Import process finished! Processed ${totalProductsProcessed} products.`);
        resolve();
      }
    });

    jsonStream.on('error', (err) => {
      console.error('An error occurred during streaming:', err);
      reject(err);
    });
  });
}

main().catch((error) => {
  console.error('\nAn unexpected error occurred:');
  console.error(error);
  process.exit(1);
});