"use client";

import { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Input } from "@/components/ui/input";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Search, Package, AlertCircle, Clock, Truck } from "lucide-react";
import { OrdersManagement } from "@/components/admin/OrdersManagement";

export default function OrdersPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");

  // Get orders data
  const orders = useQuery(api.orders.getOrders, {
    paginationOpts: { numItems: 50, cursor: null },
    status: statusFilter === "all" ? undefined : (statusFilter as "new" | "sourcing" | "action_required" | "shipped" | "delivered" | "cancelled"),
  });

  // Filter orders based on search term
  const ordersArray = Array.isArray(orders) ? orders : orders?.page || [];
  const filteredOrders = ordersArray.filter((order: Record<string, unknown>) =>
    String(order._id).toLowerCase().includes(searchTerm.toLowerCase()) ||
    (Array.isArray(order.items) && order.items.some((item: Record<string, unknown>) => String(item.title).toLowerCase().includes(searchTerm.toLowerCase())))
  );

  // Calculate summary stats
  const totalOrders = filteredOrders.length;
  const newOrders = filteredOrders.filter((order: Record<string, unknown>) => order.status === "new").length;
  const actionRequiredOrders = filteredOrders.filter((order: Record<string, unknown>) => order.status === "action_required").length;
  const shippedOrders = filteredOrders.filter((order: Record<string, unknown>) => order.status === "shipped").length;

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Orders
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Manage customer orders and fulfillment
          </p>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Total Orders</p>
                <p className="text-2xl font-bold">{totalOrders}</p>
              </div>
              <Package className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">New Orders</p>
                <p className="text-2xl font-bold">{newOrders}</p>
              </div>
              <Clock className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Action Required</p>
                <p className="text-2xl font-bold">{actionRequiredOrders}</p>
              </div>
              <AlertCircle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600 dark:text-gray-400">Shipped</p>
                <p className="text-2xl font-bold">{shippedOrders}</p>
              </div>
              <Truck className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filters</CardTitle>
          <CardDescription>
            Search and filter orders
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search orders by ID or product..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Status Filter */}
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="new">New</SelectItem>
                <SelectItem value="sourcing">Sourcing</SelectItem>
                <SelectItem value="action_required">Action Required</SelectItem>
                <SelectItem value="shipped">Shipped</SelectItem>
                <SelectItem value="delivered">Delivered</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Orders Table */}
      <Card>
        <CardHeader>
          <CardTitle>
            Orders ({filteredOrders.length})
          </CardTitle>
          <CardDescription>
            {orders ? "All orders loaded" : "Loading orders..."}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <OrdersManagement orders={filteredOrders} />
        </CardContent>
      </Card>
    </div>
  );
}
