"use client";

import React, { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  ArrowLeft, 
  Package, 
  MapPin, 
  MessageSquare, 
  Send,
  Clock,
  AlertCircle,
  CheckCircle,
  Truck,
  User
} from "lucide-react";
import Link from "next/link";
import { Id } from "@/convex/_generated/dataModel";
import { OrderStatus } from "@/lib/business/orderStatus";

interface OrderDetailPageProps {
  params: Promise<{
    id: string;
  }>;
}

interface OrderItem {
  productId: Id<"products">;
  quantity: number;
  priceAtTime: number;
  title: string;
  product?: {
    title: string;
    images: string[];
    supplier?: {
      name: string;
      id: Id<"suppliers">;
    } | null;
  } | null;
}

interface CommunicationMessage {
  message: string;
  fromAdmin: boolean;
  adminUserId?: Id<"users">;
  timestamp: number;
}



export default function OrderDetailPage({ params }: OrderDetailPageProps) {
  const [newMessage, setNewMessage] = useState("");
  const [newStatus, setNewStatus] = useState("");
  const [trackingNumber, setTrackingNumber] = useState("");
  const [orderId, setOrderId] = useState<string | null>(null);

  // Handle async params in Next.js 15
  React.useEffect(() => {
    params.then(p => setOrderId(p.id));
  }, [params]);

  const order = useQuery(api.orders.getOrder,
    orderId ? { id: orderId as Id<"orders"> } : "skip"
  );

  const updateOrderStatus = useMutation(api.orders.updateOrderStatus);
  const addCommunication = useMutation(api.orders.addOrderCommunication);

  if (!order) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading order...</p>
        </div>
      </div>
    );
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      new: { variant: "default" as const, icon: Clock, label: "New" },
      sourcing: { variant: "secondary" as const, icon: Package, label: "Sourcing" },
      action_required: { variant: "destructive" as const, icon: AlertCircle, label: "Action Required" },
      shipped: { variant: "outline" as const, icon: Truck, label: "Shipped" },
      delivered: { variant: "default" as const, icon: CheckCircle, label: "Delivered" },
      cancelled: { variant: "destructive" as const, icon: AlertCircle, label: "Cancelled" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || {
      variant: "outline" as const,
      icon: Clock,
      label: status
    };

    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center space-x-1">
        <Icon className="h-3 w-3" />
        <span>{config.label}</span>
      </Badge>
    );
  };

  const handleStatusUpdate = async () => {
    if (!newStatus) return;

    try {
      await updateOrderStatus({
        id: order._id,
        status: newStatus as OrderStatus,
        trackingNumber: trackingNumber || undefined,
      });
      setNewStatus("");
      setTrackingNumber("");
    } catch (error) {
      console.error("Error updating order status:", error);
      alert("Failed to update order status");
    }
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim()) return;

    try {
      await addCommunication({
        id: order._id,
        message: newMessage.trim(),
      });
      setNewMessage("");
    } catch (error) {
      console.error("Error sending message:", error);
      alert("Failed to send message");
    }
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard/orders">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Orders
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Order #{order._id.slice(-8)}
            </h1>
            <div className="flex items-center space-x-2 mt-2">
              {getStatusBadge(order.status)}
              <span className="text-sm text-gray-500 dark:text-gray-400">
                Created {new Date(order._creationTime).toLocaleDateString()}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Order Items */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Package className="h-5 w-5 mr-2" />
                Order Items
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {order.items.map((item: OrderItem, index: number) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <h4 className="font-medium">{item.title}</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Quantity: {item.quantity}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">${(item.priceAtTime * item.quantity).toFixed(2)}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        ${item.priceAtTime} each
                      </p>
                    </div>
                  </div>
                ))}
                <div className="border-t pt-4">
                  <div className="flex justify-between items-center">
                    <span className="font-semibold">Total:</span>
                    <span className="font-bold text-lg">${order.totalAmount.toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Communication History */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <MessageSquare className="h-5 w-5 mr-2" />
                Communication History
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {order.communicationHistory.length === 0 ? (
                  <p className="text-gray-500 dark:text-gray-400 text-center py-4">
                    No messages yet
                  </p>
                ) : (
                  order.communicationHistory.map((comm: CommunicationMessage, index: number) => (
                    <div
                      key={index}
                      className={`p-3 rounded-lg ${
                        comm.fromAdmin
                          ? "bg-blue-50 dark:bg-blue-900/20 ml-8"
                          : "bg-gray-50 dark:bg-gray-800 mr-8"
                      }`}
                    >
                      <div className="flex items-center space-x-2 mb-1">
                        <User className="h-4 w-4" />
                        <span className="text-sm font-medium">
                          {comm.fromAdmin ? "Admin" : "Customer"}
                        </span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          {new Date(comm.timestamp).toLocaleString()}
                        </span>
                      </div>
                      <p className="text-sm">{comm.message}</p>
                    </div>
                  ))
                )}
              </div>

              {/* Send Message */}
              <div className="mt-4 pt-4 border-t">
                <div className="flex space-x-2">
                  <Input
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    placeholder="Type a message to the customer..."
                    onKeyDown={(e) => e.key === "Enter" && handleSendMessage()}
                  />
                  <Button onClick={handleSendMessage} disabled={!newMessage.trim()}>
                    <Send className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Order Status */}
          <Card>
            <CardHeader>
              <CardTitle>Order Status</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center">
                <div className="mb-2">{getStatusBadge(order.status)}</div>
                {order.trackingNumber && (
                  <div className="text-sm">
                    <span className="text-gray-600 dark:text-gray-400">Tracking: </span>
                    <span className="font-mono">{order.trackingNumber}</span>
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <label className="block text-sm font-medium">Update Status</label>
                <Select value={newStatus} onValueChange={setNewStatus}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select new status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="new">New</SelectItem>
                    <SelectItem value="sourcing">Sourcing</SelectItem>
                    <SelectItem value="action_required">Action Required</SelectItem>
                    <SelectItem value="shipped">Shipped</SelectItem>
                    <SelectItem value="delivered">Delivered</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>

                {(newStatus === "shipped" || newStatus === "delivered") && (
                  <Input
                    value={trackingNumber}
                    onChange={(e) => setTrackingNumber(e.target.value)}
                    placeholder="Tracking number (optional)"
                  />
                )}

                <Button 
                  onClick={handleStatusUpdate} 
                  disabled={!newStatus}
                  className="w-full"
                >
                  Update Status
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Shipping Address */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <MapPin className="h-5 w-5 mr-2" />
                Shipping Address
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-1 text-sm">
                <div className="font-medium">{order.shippingAddress.name}</div>
                <div>{order.shippingAddress.address}</div>
                <div>
                  {order.shippingAddress.city}, {order.shippingAddress.postalCode}
                </div>
                <div>{order.shippingAddress.country}</div>
              </div>
            </CardContent>
          </Card>

          {/* Order Info */}
          <Card>
            <CardHeader>
              <CardTitle>Order Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Order ID:</span>
                <span className="font-mono text-xs">{order._id}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Created:</span>
                <span>{new Date(order._creationTime).toLocaleDateString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Items:</span>
                <span>{order.items.length}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Total:</span>
                <span className="font-medium">${order.totalAmount.toFixed(2)}</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
