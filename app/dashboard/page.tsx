"use client";

import Link from "next/link";
import { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { DatePickerWithRange } from "@/components/ui/date-range-picker";
import {
  Package,
  ShoppingCart,
  TrendingUp,
  AlertCircle,
  Plus,
  Eye,
  Calendar,
} from "lucide-react";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
} from "recharts";
import { Doc, Id } from "@/convex/_generated/dataModel";
import { DateRange } from "react-day-picker";

interface OrderWithDetails extends Doc<"orders"> {
  _id: Id<"orders">;
  _creationTime: number;
  status: "new" | "sourcing" | "action_required" | "shipped" | "delivered" | "cancelled";
  totalAmount: number;
  user?: {
    name?: string;
    email?: string;
    id: Id<"users">;
  } | null;
  assignedUser?: {
    name?: string;
    email?: string;
    id: Id<"users">;
  } | null;
  items: Array<{
    productId: Id<"products">;
    quantity: number;
    priceAtTime: number;
    title: string;
  }>;
}


export default function DashboardPage() {
  // Date range states
  const [salesDateRange, setSalesDateRange] = useState<DateRange | undefined>();
  const [orderStatsDateRange, setOrderStatsDateRange] = useState<DateRange | undefined>();

  // Convert date ranges to timestamps for API calls
  const salesStartDate = salesDateRange?.from ? Math.floor(salesDateRange.from.getTime() / 1000) : undefined;
  const salesEndDate = salesDateRange?.to ? Math.floor(salesDateRange.to.getTime() / 1000) : undefined;
  const orderStatsStartDate = orderStatsDateRange?.from ? Math.floor(orderStatsDateRange.from.getTime() / 1000) : undefined;
  const orderStatsEndDate = orderStatsDateRange?.to ? Math.floor(orderStatsDateRange.to.getTime() / 1000) : undefined;

  // Get dashboard data using efficient aggregates with optional date filtering
  const productStats = useQuery(api.products.getProductStats) as { total?: number } | null | undefined;
  const lowStockProducts = useQuery(api.products.getLowStockProducts);
  const orderStats = useQuery(api.orders.getOrderStats, {
    startDate: orderStatsStartDate,
    endDate: orderStatsEndDate,
  });
  const monthlySalesData = useQuery(api.orders.getMonthlySales, {
    startDate: salesStartDate,
    endDate: salesEndDate,
  });
  const orders = useQuery(api.orders.getOrders, {});

  // Calculate metrics using aggregate data
  const ordersArray = Array.isArray(orders) ? orders : (orders as { page?: OrderWithDetails[] })?.page || [];

  const totalProducts = productStats?.total ?? 0;
  const totalOrders = orderStats?.total ?? 0;
  const actionRequiredOrders = orderStats?.actionRequired ?? 0;
  const newOrders = orderStats?.new ?? 0;

  // Use aggregate monthly sales data, format for chart
  const salesData = (monthlySalesData || []).map(item => ({
    name: item.month,
    sales: item.sales,
    orders: item.orders,
  }));

  const metrics = [
    {
      title: "Total Products",
      value: totalProducts,
      description: "Active products in catalog",
      icon: Package,
      color: "text-blue-600",
      bgColor: "bg-blue-100",
    },
    {
      title: "Total Orders",
      value: totalOrders,
      description: "All time orders",
      icon: ShoppingCart,
      color: "text-green-600",
      bgColor: "bg-green-100",
    },
    {
      title: "New Orders",
      value: newOrders,
      description: "Awaiting processing",
      icon: TrendingUp,
      color: "text-purple-600",
      bgColor: "bg-purple-100",
    },
    {
      title: "Action Required",
      value: actionRequiredOrders,
      description: "Orders needing attention",
      icon: AlertCircle,
      color: "text-red-600",
      bgColor: "bg-red-100",
    },
  ];

  const quickActions = [
    {
      title: "Add Product",
      description: "Add a new product to catalog",
      href: "/dashboard/products/new",
      icon: Plus,
      color: "bg-blue-600 hover:bg-blue-700",
    },
    {
      title: "View Orders",
      description: "Manage customer orders",
      href: "/dashboard/orders",
      icon: Eye,
      color: "bg-green-600 hover:bg-green-700",
    },
  ];

  const orderStatusData = [
    { name: "New", value: newOrders },
    { name: "Sourcing", value: orderStats?.sourcing ?? 0 },
    { name: "Shipped", value: orderStats?.shipped ?? 0 },
    { name: "Delivered", value: orderStats?.delivered ?? 0 },
    { name: "Action Required", value: actionRequiredOrders },
  ];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
          Dashboard
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Welcome to your MaoMao admin dashboard. Here&apos;s what&apos;s happening with your business.
        </p>
      </div>

      {/* Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {metrics.map((metric) => {
          const Icon = metric.icon;
          return (
            <Card key={metric.title}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {metric.title}
                </CardTitle>
                <div className={`p-2 rounded-lg ${metric.bgColor}`}>
                  <Icon className={`h-4 w-4 ${metric.color}`} />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900 dark:text-white">
                  {metric.value}
                </div>
                <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                  {metric.description}
                </p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>
            Common tasks to manage your business
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {quickActions.map((action) => {
              const Icon = action.icon;
              return (
                <Button
                  key={action.title}
                  variant="outline"
                  className="h-auto p-4 justify-start"
                  asChild
                >
                  <Link href={action.href}>
                    <div className={`p-2 rounded-lg ${action.color} mr-4`}>
                      <Icon className="h-4 w-4 text-white" />
                    </div>
                    <div className="text-left">
                      <div className="font-medium">{action.title}</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        {action.description}
                      </div>
                    </div>
                  </Link>
                </Button>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sales Trend */}
        <Card>
          <CardHeader>
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
              <div>
                <CardTitle>Sales Trend</CardTitle>
                <CardDescription>Monthly sales and order volume</CardDescription>
              </div>
              <div className="flex flex-col sm:flex-row gap-3 items-start sm:items-center">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Calendar className="h-4 w-4" />
                  <span>Date Range</span>
                </div>
                <DatePickerWithRange
                  dateRange={salesDateRange}
                  onDateRangeChange={setSalesDateRange}
                  placeholder="Select date range"
                  className="w-[280px]"
                />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={salesData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Line
                  type="monotone"
                  dataKey="sales"
                  stroke="#3b82f6"
                  strokeWidth={2}
                  name="Sales ($)"
                />
                <Line
                  type="monotone"
                  dataKey="orders"
                  stroke="#10b981"
                  strokeWidth={2}
                  name="Orders"
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Order Status Distribution */}
        <Card>
          <CardHeader>
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
              <div>
                <CardTitle>Order Status</CardTitle>
                <CardDescription>Current order distribution by status</CardDescription>
              </div>
              <div className="flex flex-col sm:flex-row gap-3 items-start sm:items-center">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Calendar className="h-4 w-4" />
                  <span>Date Range</span>
                </div>
                <DatePickerWithRange
                  dateRange={orderStatsDateRange}
                  onDateRangeChange={setOrderStatsDateRange}
                  placeholder="Select date range"
                  className="w-[280px]"
                />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={orderStatusData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="value" fill="#3b82f6" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Orders */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Orders</CardTitle>
            <CardDescription>Latest customer orders</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {ordersArray.slice(0, 5).map((order: OrderWithDetails) => (
                <div key={order._id} className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-sm">Order #{order._id.slice(-6)}</p>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      {order.items.length} items • ${order.totalAmount}
                    </p>
                  </div>
                  <Badge
                    variant={
                      order.status === "new" ? "default" :
                      order.status === "action_required" ? "destructive" :
                      order.status === "shipped" ? "secondary" :
                      "outline"
                    }
                  >
                    {order.status.replace("_", " ")}
                  </Badge>
                </div>
              )) || (
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  No orders yet
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Low Stock Products */}
        <Card>
          <CardHeader>
            <CardTitle>Low Stock Alert</CardTitle>
            <CardDescription>Products running low on inventory</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {lowStockProducts && lowStockProducts.length > 0 ? (
                lowStockProducts.slice(0, 5).map((product) => (
                  <div key={product._id} className="flex items-center justify-between">
                    <div>
                      <p className="font-medium text-sm">{product.title}</p>
                      <p className="text-xs text-gray-600 dark:text-gray-400">
                        Stock: {product.stockCount} units
                      </p>
                    </div>
                    <Badge variant="destructive">
                      Low Stock
                    </Badge>
                  </div>
                ))
              ) : (
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  All products well stocked
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
