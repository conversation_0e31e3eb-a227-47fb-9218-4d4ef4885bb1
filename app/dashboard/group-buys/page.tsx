"use client";

import React, { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  TrendingUp,
  Search,
  Filter,
  Plus,
  Users,
  Target,
} from "lucide-react";
import { GroupBuysManagement } from "@/components/admin/GroupBuysManagement";
import Link from "next/link";
import { Doc } from "@/convex/_generated/dataModel";

type GroupBuyStatus = "active" | "completed" | "expired";

interface GroupBuyWithProduct extends Doc<"groupBuys"> {
  product?: {
    title?: string;
    images?: string[];
  } | null;
}

export default function GroupBuysPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");

  // Fetch group buys
  const groupBuys = useQuery(api.groupBuys.getGroupBuys, {
    status: statusFilter === "all" ? undefined : (statusFilter as GroupBuyStatus)
  });

  // Filter group buys based on search
  const filteredGroupBuys = (groupBuys || []).filter((groupBuy: GroupBuyWithProduct) =>
    groupBuy.product?.title?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Calculate stats
  const totalGroupBuys = groupBuys?.length || 0;
  const activeGroupBuys = groupBuys?.filter((gb: GroupBuyWithProduct) => gb.status === "active").length || 0;
  const completedGroupBuys = groupBuys?.filter((gb: GroupBuyWithProduct) => gb.status === "completed").length || 0;
  const totalParticipants = groupBuys?.reduce((sum: number, gb: GroupBuyWithProduct) => sum + gb.currentParticipants, 0) || 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Group Buys</h1>
          <p className="text-gray-500 dark:text-gray-400">
            Manage group buying campaigns and track participation
          </p>
        </div>
        <Link href="/dashboard/group-buys/new">
          <Button>
            <Plus className="w-4 h-4 mr-2" />
            Create Group Buy
          </Button>
        </Link>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Group Buys</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalGroupBuys}</div>
            <p className="text-xs text-muted-foreground">
              All campaigns
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Campaigns</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{activeGroupBuys}</div>
            <p className="text-xs text-muted-foreground">
              Currently running
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completedGroupBuys}</div>
            <p className="text-xs text-muted-foreground">
              Successfully completed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Participants</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalParticipants}</div>
            <p className="text-xs text-muted-foreground">
              Across all campaigns
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Group Buys Table */}
      <Card>
        <CardHeader>
          <CardTitle>Group Buy Campaigns</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search group buys by product name..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <Filter className="w-4 h-4 mr-2" />
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Campaigns</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="expired">Expired</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <GroupBuysManagement groupBuys={filteredGroupBuys} />

          {/* Loading state */}
          <div className="text-center py-4 text-sm text-gray-500 dark:text-gray-400">
            {groupBuys ? "All group buys loaded" : "Loading group buys..."}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
