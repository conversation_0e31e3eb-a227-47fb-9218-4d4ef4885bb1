"use client";

import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { CreateGroupBuyForm } from "@/components/admin/CreateGroupBuyForm";

export default function NewGroupBuyPage() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Link href="/dashboard/group-buys">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Group Buys
          </Button>
        </Link>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Create Group Buy</h1>
          <p className="text-gray-500 dark:text-gray-400">
            Start a new group buying campaign
          </p>
        </div>
      </div>

      <CreateGroupBuyForm />
    </div>
  );
}