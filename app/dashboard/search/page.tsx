"use client";

import { useState } from "react";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Search,
  Eye,
  DollarSign,
  Package,
  Sparkles,
  ArrowLeft
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import VisualSearchWidget from "@/components/search/VisualSearchWidget";
import { Doc } from "@/convex/_generated/dataModel";

type ProductStatus = "active" | "inactive" | "archived";
type StockFilter = "in_stock" | "low_stock" | "out_of_stock";

interface ProductWithDetails extends Doc<"products"> {
  supplier?: {
    name: string;
    id: string;
  } | null;
}

export default function SearchPage() {
  const [activeTab, setActiveTab] = useState("text");
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [priceRange, setPriceRange] = useState({ min: "", max: "" });
  const [stockFilter, setStockFilter] = useState<string>("all");
  const [visualSearchResults, setVisualSearchResults] = useState<ProductWithDetails[]>([]);

  // Text search results
  const textSearchResults = useQuery(api.products.advancedSearchProducts, {
    searchTerm: searchTerm || undefined,
    status: statusFilter === "all" ? undefined : (statusFilter as ProductStatus),
    priceRange: priceRange.min && priceRange.max ? {
      min: parseFloat(priceRange.min) || 0,
      max: parseFloat(priceRange.max) || 999999,
    } : undefined,
    stockFilter: stockFilter === "all" ? undefined : (stockFilter as StockFilter),
    limit: 20,
  });

  const getStockBadge = (stock: number) => {
    if (stock === 0) {
      return <Badge variant="destructive">Out of Stock</Badge>;
    } else if (stock < 10) {
      return <Badge variant="destructive">Low Stock</Badge>;
    } else {
      return <Badge variant="default">In Stock</Badge>;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return <Badge variant="default">Active</Badge>;
      case "inactive":
        return <Badge variant="secondary">Inactive</Badge>;
      case "archived":
        return <Badge variant="outline">Archived</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const ProductCard = ({ product, showSimilarity = false }: { product: ProductWithDetails; showSimilarity?: boolean }) => (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Product Image */}
          {product.images.length > 0 && (
            <div className="aspect-square rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-800">
              <Image
                src={typeof product.images[0] === 'string' ? product.images[0] : ''}
                alt={product.title}
                width={200}
                height={200}
                className="w-full h-full object-cover"
              />
            </div>
          )}

          {/* Product Info */}
          <div className="space-y-2">
            <h3 className="font-medium text-sm line-clamp-2">
              {product.title}
            </h3>
            
            <p className="text-xs text-gray-600 dark:text-gray-400 line-clamp-2">
              {product.description}
            </p>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-1">
                <DollarSign className="h-3 w-3 text-gray-500" />
                <span className="text-sm font-medium">
                  ${product.finalPrice}
                </span>
              </div>
              <div className="flex items-center space-x-1">
                <Package className="h-3 w-3 text-gray-500" />
                <span className="text-xs text-gray-500">
                  {product.stockCount}
                </span>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex space-x-1">
                {getStatusBadge(product.status)}
                {showSimilarity && product.similarity && (
                  <Badge variant="outline" className="text-xs">
                    {Math.round(product.similarity * 100)}% match
                  </Badge>
                )}
              </div>
              {getStockBadge(product.stockCount)}
            </div>

            {/* Tags */}
            <div className="flex flex-wrap gap-1">
              {product.tags.slice(0, 3).map((tag: string) => (
                <Badge key={tag} variant="outline" className="text-xs px-1 py-0">
                  {tag}
                </Badge>
              ))}
              {product.tags.length > 3 && (
                <Badge variant="outline" className="text-xs px-1 py-0">
                  +{product.tags.length - 3}
                </Badge>
              )}
            </div>

            {/* Actions */}
            <Button
              size="sm"
              variant="outline"
              className="w-full"
              asChild
            >
              <Link href={`/dashboard/products/${product._id}`}>
                <Eye className="h-3 w-3 mr-1" />
                View Details
              </Link>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/dashboard/products">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Products
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Advanced Product Search
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Search products using text or AI-powered visual similarity
            </p>
          </div>
        </div>
      </div>

      {/* Search Interface */}
      <Card>
        <CardHeader>
          <CardTitle>Search Products</CardTitle>
          <CardDescription>
            Use text search or upload an image to find products
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="text" className="flex items-center space-x-2">
                <Search className="h-4 w-4" />
                <span>Text Search</span>
              </TabsTrigger>
              <TabsTrigger value="visual" className="flex items-center space-x-2">
                <Sparkles className="h-4 w-4" />
                <span>Visual Search</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="text" className="space-y-4">
              {/* Text Search Form */}
              <div className="space-y-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search products by title, description, or tags..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>

                {/* Filters */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                      <SelectItem value="archived">Archived</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={stockFilter} onValueChange={setStockFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="Stock Level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Stock Levels</SelectItem>
                      <SelectItem value="in_stock">In Stock (&gt;10)</SelectItem>
                      <SelectItem value="low_stock">Low Stock (1-10)</SelectItem>
                      <SelectItem value="out_of_stock">Out of Stock</SelectItem>
                    </SelectContent>
                  </Select>

                  <Input
                    placeholder="Min Price $"
                    value={priceRange.min}
                    onChange={(e) => setPriceRange(prev => ({ ...prev, min: e.target.value }))}
                    type="number"
                  />

                  <Input
                    placeholder="Max Price $"
                    value={priceRange.max}
                    onChange={(e) => setPriceRange(prev => ({ ...prev, max: e.target.value }))}
                    type="number"
                  />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="visual">
              <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                <div className="lg:col-span-1">
                  <VisualSearchWidget 
                    onResultsChange={setVisualSearchResults}
                    maxResults={12}
                    threshold={0.5}
                  />
                </div>
                <div className="lg:col-span-3">
                  {visualSearchResults.length > 0 && (
                    <Card>
                      <CardHeader>
                        <CardTitle>Visual Search Results</CardTitle>
                        <CardDescription>
                          {visualSearchResults.length} visually similar products found
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                          {visualSearchResults.map((product) => (
                            <ProductCard 
                              key={product._id} 
                              product={product} 
                              showSimilarity={true}
                            />
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Text Search Results */}
      {activeTab === "text" && textSearchResults && textSearchResults.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Search Results</CardTitle>
            <CardDescription>
              {textSearchResults.length} products found
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {textSearchResults.map((product) => (
                <ProductCard key={product._id} product={product} />
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* No Results */}
      {activeTab === "text" && textSearchResults && textSearchResults.length === 0 && searchTerm && (
        <Card>
          <CardContent className="text-center py-8">
            <Search className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <p className="text-gray-500 dark:text-gray-400 mb-2">
              No products found for &quot;{searchTerm}&quot;
            </p>
            <p className="text-sm text-gray-400">
              Try adjusting your search terms or filters
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
