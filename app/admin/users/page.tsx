"use client";

import { useState } from "react";
import { useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { withPermission, PermissionGuard, PERMISSIONS, type Role, type Permission } from "@/lib/auth/permissions";
import type { Id } from "@/convex/_generated/dataModel";
import ErrorBoundary from "@/components/ErrorBoundary";
import CreateAdminUserForm from "@/components/admin/CreateAdminUserForm";

interface AdminUserWithUserDetails {
  _id: Id<"adminUsers">;
  _creationTime: number; // All Convex documents have _creationTime
  userId: Id<"users">;
  role: Role;
  permissions: readonly Permission[];
  createdBy: Id<"users">;
  isActive: boolean;
  lastLoginAt?: number;
  user: {
    name: string;
    email: string;
    image: string | undefined;
  } | null;
}

function AdminUsersPage() {
  // Temporarily disabled due to type instantiation issues
  // const adminUsers = useQuery(api.auth.rbac.getAdminUsers) as unknown;
  const adminUsers: AdminUserWithUserDetails[] = [];
  const createAdminUser = useMutation(api.auth.rbac.createAdminUser);
  const updateAdminUserRole = useMutation(api.auth.rbac.updateAdminUserRole);
  const deactivateAdminUser = useMutation(api.auth.rbac.deactivateAdminUser);
  
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedUser, setSelectedUser] = useState<AdminUserWithUserDetails | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  const handleCreateAdmin = async (formData: {
    userId: Id<"users">;
    role: Role;
  }) => {
    setIsLoading(true);
    setError("");
    
    try {
      await createAdminUser(formData);
      setShowCreateForm(false);
    } catch (err: unknown) {
      setError(err instanceof Error ? err.message : "An unknown error occurred.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpdateRole = async (adminUserId: Id<"adminUsers">, role: Role) => {
    setIsLoading(true);
    setError("");
    
    try {
      await updateAdminUserRole({ adminUserId, role });
      setSelectedUser(null);
    } catch (err: unknown) {
      setError(err instanceof Error ? err.message : "An unknown error occurred.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeactivate = async (adminUserId: Id<"adminUsers">) => {
    if (!confirm("Are you sure you want to deactivate this admin user?")) {
      return;
    }
    
    setIsLoading(true);
    setError("");
    
    try {
      await deactivateAdminUser({ adminUserId });
    } catch (err: unknown) {
      setError(err instanceof Error ? err.message : "An unknown error occurred.");
    } finally {
      setIsLoading(false);
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case "super_admin":
        return "bg-red-100 text-red-800";
      case "admin":
        return "bg-blue-100 text-blue-800";
      case "moderator":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (!adminUsers) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <ErrorBoundary
      onError={(error, errorInfo) => {
        console.error('Admin users page error caught by ErrorBoundary:', error, errorInfo);
      }}
      enableRetry={true}
      maxRetries={3}
      showErrorDetails={process.env.NODE_ENV === 'development'}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-semibold text-gray-900">Admin Users</h1>
          <p className="mt-2 text-sm text-gray-700">
            Manage admin users and their roles in the system.
          </p>
        </div>
        <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
          <PermissionGuard permission={PERMISSIONS.ADMIN_USERS_CREATE}>
            <button
              type="button"
              onClick={() => setShowCreateForm(true)}
              className="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:w-auto"
            >
              Add Admin User
            </button>
          </PermissionGuard>
        </div>
      </div>

      {error && (
        <div className="mt-4 rounded-md bg-red-50 p-4">
          <div className="text-sm text-red-700">{error}</div>
        </div>
      )}

      {showCreateForm && (
        <CreateAdminUserForm
          onClose={() => setShowCreateForm(false)}
          onCreate={handleCreateAdmin}
          isLoading={isLoading}
          error={error}
        />
      )}
 
       <div className="mt-8 flex flex-col">
         <div className="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
           <div className="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
            <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
              <table className="min-w-full divide-y divide-gray-300">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                      User
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Role
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Last Login
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wide">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {(adminUsers as AdminUserWithUserDetails[] || []).map((adminUser) => (
                    <tr key={adminUser._id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                              <span className="text-sm font-medium text-gray-700">
                                {adminUser.user?.name?.charAt(0) || "?"}
                              </span>
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {adminUser.user?.name || "Unknown"}
                            </div>
                            <div className="text-sm text-gray-500">
                              {adminUser.user?.email || "No email"}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRoleBadgeColor(adminUser.role)}`}>
                          {adminUser.role.replace("_", " ")}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          adminUser.isActive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                        }`}>
                          {adminUser.isActive ? "Active" : "Inactive"}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {adminUser.lastLoginAt 
                          ? new Date(adminUser.lastLoginAt).toLocaleDateString()
                          : "Never"
                        }
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex space-x-2">
                          <PermissionGuard permission={PERMISSIONS.ADMIN_USERS_EDIT}>
                            <button
                              onClick={() => setSelectedUser(adminUser)}
                              className="text-blue-600 hover:text-blue-900"
                            >
                              Edit
                            </button>
                          </PermissionGuard>
                          <PermissionGuard permission={PERMISSIONS.ADMIN_USERS_DELETE}>
                            {adminUser.isActive && (
                              <button
                                onClick={() => handleDeactivate(adminUser._id)}
                                className="text-red-600 hover:text-red-900"
                                disabled={isLoading}
                              >
                                Deactivate
                              </button>
                            )}
                          </PermissionGuard>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      {/* Edit Role Modal */}
      {selectedUser && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Update Role for {selectedUser.user?.name}
              </h3>
              <div className="space-y-3">
                {["moderator", "admin", "super_admin"].map((role) => (
                  <button
                    key={role}
                    onClick={() => handleUpdateRole(selectedUser._id, role as Role)}
                    disabled={isLoading}
                    className={`w-full text-left px-4 py-2 rounded-md border ${
                      selectedUser.role === role
                        ? "bg-blue-50 border-blue-200 text-blue-800"
                        : "bg-white border-gray-200 text-gray-700 hover:bg-gray-50"
                    }`}
                  >
                    {role.replace("_", " ").toUpperCase()}
                  </button>
                ))}
              </div>
              <div className="flex justify-end space-x-3 mt-6">
                <button
                  onClick={() => setSelectedUser(null)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      </div>
    </ErrorBoundary>
  );
}

export default withPermission(AdminUsersPage, PERMISSIONS.ADMIN_USERS_VIEW);
