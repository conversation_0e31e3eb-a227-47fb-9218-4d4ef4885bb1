"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Trash2, PlusCircle } from "lucide-react";

interface CustomService {
  name: string;
  description?: string;
  minQuantity?: number;
  price: number;
  currency: string;
  isRequired: boolean;
}

interface CustomServicesFormProps {
  customServices: CustomService[];
  onCustomServicesChange: (customServices: CustomService[]) => void;
}

type CustomServiceField = keyof CustomService;

export function CustomServicesForm({ customServices, onCustomServicesChange }: CustomServicesFormProps) {
  const handleServiceChange = <K extends CustomServiceField>(
    index: number,
    field: K,
    value: CustomService[K]
  ) => {
    const newServices = [...customServices];
    newServices[index] = { ...newServices[index], [field]: value };
    onCustomServicesChange(newServices);
  };

  const handleNumberChange = (
    index: number,
    field: 'minQuantity' | 'price',
    value: string
  ) => {
    const parsedValue = field === 'minQuantity' 
      ? (value === '' ? undefined : parseInt(value) || undefined)
      : parseFloat(value) || 0;
    
    handleServiceChange(index, field, parsedValue as CustomService[typeof field]);
  };

  const handleCheckboxChange = (index: number, checked: boolean | string) => {
    handleServiceChange(index, "isRequired", Boolean(checked));
  };

  const addService = () => {
    const newServices: CustomService[] = [...customServices, { name: "", price: 0, currency: "CNY", isRequired: false }];
    onCustomServicesChange(newServices);
  };

  const removeService = (index: number) => {
    const newServices = customServices.filter((_, i) => i !== index);
    onCustomServicesChange(newServices);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Custom Services</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {customServices.map((service, index) => (
          <div key={index} className="p-4 border rounded-md space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="block text-sm font-medium">Service Name</label>
                <Input
                  value={service.name}
                  onChange={(e) => handleServiceChange(index, "name", e.target.value)}
                  placeholder="e.g., Custom Logo, Gift Wrapping"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium">Price (CNY)</label>
                <Input
                  type="number"
                  value={service.price}
                  onChange={(e) => handleNumberChange(index, "price", e.target.value)}
                  placeholder="e.g., 50"
                  min="0"
                  step="0.01"
                />
              </div>
            </div>
            <div className="space-y-2">
              <label className="block text-sm font-medium">Description</label>
              <Input
                value={service.description ?? ""}
                onChange={(e) => handleServiceChange(index, "description", e.target.value)}
                placeholder="(optional) Describe the service"
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="block text-sm font-medium">Min Quantity</label>
                <Input
                  type="number"
                  value={service.minQuantity ?? ""}
                  onChange={(e) => handleNumberChange(index, "minQuantity", e.target.value)}
                  placeholder="(optional)"
                  min="0"
                  step="1"
                />
              </div>
              <div className="flex items-center space-x-2 pt-6">
                <Checkbox
                  id={`isRequired-${index}`}
                  checked={service.isRequired}
                  onCheckedChange={(checked: boolean | string) => handleCheckboxChange(index, checked)}
                />
                <label htmlFor={`isRequired-${index}`} className="text-sm font-medium">Is Required</label>
              </div>
            </div>
            <div className="flex justify-end">
              <Button type="button" variant="destructive" size="sm" onClick={() => removeService(index)}>
                <Trash2 className="h-4 w-4 mr-2" />
                Remove Service
              </Button>
            </div>
          </div>
        ))}
        <Button type="button" variant="outline" onClick={addService} className="mt-4">
          <PlusCircle className="h-4 w-4 mr-2" />
          Add Custom Service
        </Button>
      </CardContent>
    </Card>
  );
}
