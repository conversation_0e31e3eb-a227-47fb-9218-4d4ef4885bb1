"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Trash2, PlusCircle } from "lucide-react";

interface Variant {
  type: string;
  name: string;
  value: string;
  priceType: 'modifier' | 'absolute';
  priceModifier?: number;
  absolutePrice?: number;
  currency?: string;
  availableQuantity?: number;
  images?: string[];
}

interface VariantsFormProps {
  variants: Variant[];
  onVariantsChange: (variants: Variant[]) => void;
}

type VariantField = keyof Variant;

export function VariantsForm({ variants, onVariantsChange }: VariantsFormProps) {
  const handleVariantChange = <K extends VariantField>(
    index: number,
    field: K,
    value: Variant[K]
  ) => {
    const newVariants = [...variants];
    newVariants[index] = { ...newVariants[index], [field]: value };
    onVariantsChange(newVariants);
  };

  const handleNumberChange = (
    index: number,
    field: 'priceModifier' | 'absolutePrice' | 'availableQuantity',
    value: string
  ) => {
    const parsedValue = field === 'availableQuantity' 
      ? parseInt(value) || undefined
      : parseFloat(value) || undefined;
    
    handleVariantChange(index, field, parsedValue);
  };

  const addVariant = () => {
    const newVariants: Variant[] = [...variants, { 
      type: "", 
      name: "", 
      value: "", 
      priceType: 'modifier' 
    }];
    onVariantsChange(newVariants);
  };

  const removeVariant = (index: number) => {
    const newVariants = variants.filter((_, i) => i !== index);
    onVariantsChange(newVariants);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Product Variants</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {variants.map((variant, index) => (
          <div key={index} className="p-4 border rounded-md space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <label className="block text-sm font-medium">Type</label>
                <Input
                  value={variant.type}
                  onChange={(e) => handleVariantChange(index, "type", e.target.value)}
                  placeholder="e.g., Color, Size"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium">Name</label>
                <Input
                  value={variant.name}
                  onChange={(e) => handleVariantChange(index, "name", e.target.value)}
                  placeholder="e.g., Red, Large"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium">Value</label>
                <Input
                  value={variant.value}
                  onChange={(e) => handleVariantChange(index, "value", e.target.value)}
                  placeholder="e.g., #FF0000, XL"
                />
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <label className="block text-sm font-medium">Price Type</label>
                <Select
                  value={variant.priceType}
                  onValueChange={(value: 'modifier' | 'absolute') => 
                    handleVariantChange(index, "priceType", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="modifier">Modifier</SelectItem>
                    <SelectItem value="absolute">Absolute</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              {variant.priceType === 'modifier' ? (
                <div className="space-y-2">
                  <label className="block text-sm font-medium">Price Modifier</label>
                  <Input
                    type="number"
                    value={variant.priceModifier ?? ""}
                    onChange={(e) => handleNumberChange(index, "priceModifier", e.target.value)}
                    placeholder="e.g., 10 or -5"
                    step="0.01"
                  />
                </div>
              ) : (
                <div className="space-y-2">
                  <label className="block text-sm font-medium">Absolute Price</label>
                  <Input
                    type="number"
                    value={variant.absolutePrice ?? ""}
                    onChange={(e) => handleNumberChange(index, "absolutePrice", e.target.value)}
                    placeholder="e.g., 99.99"
                    step="0.01"
                  />
                </div>
              )}
              <div className="space-y-2">
                <label className="block text-sm font-medium">Available Quantity</label>
                <Input
                  type="number"
                  value={variant.availableQuantity ?? ""}
                  onChange={(e) => handleNumberChange(index, "availableQuantity", e.target.value)}
                  placeholder="(optional)"
                  min="0"
                  step="1"
                />
              </div>
            </div>
            <div className="flex justify-end">
              <Button 
                type="button" 
                variant="destructive" 
                size="sm" 
                onClick={() => removeVariant(index)}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Remove Variant
              </Button>
            </div>
          </div>
        ))}
        <Button type="button" variant="outline" onClick={addVariant} className="mt-4">
          <PlusCircle className="h-4 w-4 mr-2" />
          Add Variant
        </Button>
      </CardContent>
    </Card>
  );
}
