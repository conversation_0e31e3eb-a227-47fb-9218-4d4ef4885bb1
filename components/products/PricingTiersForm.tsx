"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Trash2, PlusCircle } from "lucide-react";

interface PricingTier {
  minQuantity: number;
  maxQuantity?: number;
  price: number;
  currency: string;
  discountPercentage?: number;
}

interface PricingTiersFormProps {
  pricingTiers: PricingTier[];
  onPricingTiersChange: (pricingTiers: PricingTier[]) => void;
}

type PricingTierField = keyof PricingTier;

export function PricingTiersForm({ pricingTiers, onPricingTiersChange }: PricingTiersFormProps) {
  const handleTierChange = <K extends PricingTierField>(
    index: number,
    field: K,
    value: PricingTier[K]
  ) => {
    const newTiers = [...pricingTiers];
    newTiers[index] = { ...newTiers[index], [field]: value };
    onPricingTiersChange(newTiers);
  };

  const handleNumberChange = (
    index: number,
    field: 'minQuantity' | 'maxQuantity' | 'price' | 'discountPercentage',
    value: string
  ) => {
    let parsedValue: number | undefined;
    
    if (field === 'maxQuantity' || field === 'discountPercentage') {
      parsedValue = value === '' ? undefined : parseFloat(value) || undefined;
    } else {
      parsedValue = parseFloat(value) || 0;
    }
    
    handleTierChange(index, field, parsedValue as PricingTier[typeof field]);
  };

  const addTier = () => {
    const newTiers: PricingTier[] = [...pricingTiers, { minQuantity: 0, price: 0, currency: "CNY" }];
    onPricingTiersChange(newTiers);
  };

  const removeTier = (index: number) => {
    const newTiers = pricingTiers.filter((_, i) => i !== index);
    onPricingTiersChange(newTiers);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Pricing Tiers</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {pricingTiers.map((tier, index) => (
          <div key={index} className="flex items-end space-x-4 p-4 border rounded-md">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 flex-grow">
              <div className="space-y-2">
                <label className="block text-sm font-medium">Min Quantity</label>
                <Input
                  type="number"
                  value={tier.minQuantity}
                  onChange={(e) => handleNumberChange(index, "minQuantity", e.target.value)}
                  placeholder="e.g., 10"
                  min="0"
                  step="1"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium">Max Quantity</label>
                <Input
                  type="number"
                  value={tier.maxQuantity ?? ""}
                  onChange={(e) => handleNumberChange(index, "maxQuantity", e.target.value)}
                  placeholder="e.g., 50 (optional)"
                  min="0"
                  step="1"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium">Price (CNY)</label>
                <Input
                  type="number"
                  value={tier.price}
                  onChange={(e) => handleNumberChange(index, "price", e.target.value)}
                  placeholder="e.g., 85.50"
                  min="0"
                  step="0.01"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium">Discount (%)</label>
                <Input
                  type="number"
                  value={tier.discountPercentage ?? ""}
                  onChange={(e) => handleNumberChange(index, "discountPercentage", e.target.value)}
                  placeholder="e.g., 10 (optional)"
                  min="0"
                  max="100"
                  step="0.1"
                />
              </div>
            </div>
            <Button type="button" variant="destructive" size="icon" onClick={() => removeTier(index)}>
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        ))}
        <Button type="button" variant="outline" onClick={addTier} className="mt-4">
          <PlusCircle className="h-4 w-4 mr-2" />
          Add Pricing Tier
        </Button>
      </CardContent>
    </Card>
  );
}
