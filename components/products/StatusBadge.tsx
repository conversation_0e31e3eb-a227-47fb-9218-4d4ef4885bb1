"use client";

import { Badge } from "@/components/ui/badge";
import { ProductStatus } from "@/app/productRedesignMockData";

interface StatusBadgeProps {
  status: ProductStatus;
}

interface StockBadgeProps {
  stockCount: number;
}

export function StatusBadge({ status }: StatusBadgeProps) {
  const getStatusConfig = (status: ProductStatus) => {
    switch (status) {
      case ProductStatus.ACTIVE:
        return { 
          variant: "default" as const, 
          label: "Active",
          className: "bg-success text-success-foreground"
        };
      case ProductStatus.INACTIVE:
        return { 
          variant: "secondary" as const, 
          label: "Inactive",
          className: "bg-warning text-warning-foreground"
        };
      case ProductStatus.ARCHIVED:
        return { 
          variant: "outline" as const, 
          label: "Archived",
          className: "border-muted-foreground/50"
        };
      default:
        return { 
          variant: "outline" as const, 
          label: status,
          className: ""
        };
    }
  };

  const config = getStatusConfig(status);
  
  return (
    <Badge variant={config.variant} className={config.className}>
      {config.label}
    </Badge>
  );
}

export function StockBadge({ stockCount }: StockBadgeProps) {
  const getStockConfig = (stock: number) => {
    if (stock === 0) {
      return {
        variant: "destructive" as const,
        label: "Out of Stock",
        className: ""
      };
    } else if (stock < 10) {
      return {
        variant: "destructive" as const,
        label: "Low Stock",
        className: "bg-warning text-warning-foreground"
      };
    } else if (stock < 50) {
      return {
        variant: "secondary" as const,
        label: "Medium Stock",
        className: ""
      };
    } else {
      return {
        variant: "default" as const,
        label: "In Stock",
        className: "bg-success text-success-foreground"
      };
    }
  };

  const config = getStockConfig(stockCount);
  
  return (
    <Badge variant={config.variant} className={config.className}>
      {config.label}
    </Badge>
  );
}