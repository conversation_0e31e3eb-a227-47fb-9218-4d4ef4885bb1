"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Trash2, PlusCircle } from "lucide-react";

interface Attribute {
  name: string;
  value: string;
}

interface AttributesFormProps {
  attributes: Attribute[];
  onAttributesChange: (attributes: Attribute[]) => void;
}

type AttributeField = keyof Attribute;

export function AttributesForm({ attributes, onAttributesChange }: AttributesFormProps) {
  const handleAttributeChange = <K extends AttributeField>(
    index: number,
    field: K,
    value: Attribute[K]
  ) => {
    const newAttributes = [...attributes];
    newAttributes[index] = { ...newAttributes[index], [field]: value };
    onAttributesChange(newAttributes);
  };

  const addAttribute = () => {
    const newAttributes: Attri<PERSON><PERSON>[] = [...attributes, { name: "", value: "" }];
    onAttributesChange(newAttributes);
  };

  const removeAttribute = (index: number) => {
    const newAttributes = attributes.filter((_, i) => i !== index);
    onAttributesChange(newAttributes);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Product Attributes</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {attributes.map((attribute, index) => (
          <div key={index} className="flex items-end space-x-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 flex-grow">
              <div className="space-y-2">
                <label className="block text-sm font-medium">Attribute Name</label>
                <Input
                  value={attribute.name}
                  onChange={(e) => handleAttributeChange(index, "name", e.target.value)}
                  placeholder="e.g., Material, Weight"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-medium">Attribute Value</label>
                <Input
                  value={attribute.value}
                  onChange={(e) => handleAttributeChange(index, "value", e.target.value)}
                  placeholder="e.g., Cotton, 2kg"
                />
              </div>
            </div>
            <Button type="button" variant="destructive" size="icon" onClick={() => removeAttribute(index)}>
              <Trash2 className="h-4 w-4" />
            </Button>
          </div>
        ))}
        <Button type="button" variant="outline" onClick={addAttribute} className="mt-4">
          <PlusCircle className="h-4 w-4 mr-2" />
          Add Attribute
        </Button>
      </CardContent>
    </Card>
  );
}
