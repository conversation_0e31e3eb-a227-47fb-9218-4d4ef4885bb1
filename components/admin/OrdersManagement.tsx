import React from "react";
import { ManagementTable } from "./ManagementTable";
import { Badge } from "@/components/ui/badge";
import { Doc } from "@/convex/_generated/dataModel";

interface OrdersManagementProps {
  orders: Doc<"orders">[];
}

export function OrdersManagement({ orders }: OrdersManagementProps) {
  if (!orders) {
    return <div>Loading...</div>;
  }

  const columns = [
    { 
      header: "Order ID", 
      accessor: (order: Doc<"orders">) => <span className="font-mono">{order._id.slice(-6)}</span>
    },
    {
      header: "Customer",
      accessor: (order: Doc<"orders">) => order.shippingAddress.name,
    },
    {
      header: "Status",
      accessor: (order: Doc<"orders">) => <Badge>{order.status}</Badge>,
    },
    {
      header: "Total",
      accessor: (order: Doc<"orders">) => `$${order.totalAmount.toFixed(2)}`,
    },
    {
      header: "Items",
      accessor: (order: Doc<"orders">) => order.items.length,
    },
    {
      header: "Date",
      accessor: (order: Doc<"orders">) => new Date(order._creationTime).toLocaleDateString(),
    },
  ];

  const handleViewDetails = (order: Doc<"orders">) => {
    // TODO: Implement view details functionality
    console.log("View details for:", order);
  };

  return (
    <ManagementTable
      data={orders}
      columns={columns}
      onViewDetails={handleViewDetails}
    />
  );
}