import React, { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Doc, Id } from "@/convex/_generated/dataModel";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Trash2, Plus } from "lucide-react";
import { useRouter } from "next/navigation";

export function CreateGroupBuyForm() {
  const router = useRouter();
  const products = useQuery(api.products.getProducts, { status: "active" });
  const createGroupBuy = useMutation(api.groupBuys.createGroupBuy);

  const [productId, setProductId] = useState<Id<"products"> | "">("");
  const [targetTiers, setTargetTiers] = useState([{ quantity: "", price: "" }]);
  const [endTime, setEndTime] = useState("");
  const [providerTiers, setProviderTiers] = useState([{ minParticipants: "", pricePerUnit: "", ourCommission: "", finalPrice: "" }]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleAddTier = () => {
    setTargetTiers([...targetTiers, { quantity: "", price: "" }]);
  };

  const handleRemoveTier = (index: number) => {
    const newTiers = targetTiers.filter((_, i) => i !== index);
    setTargetTiers(newTiers);
  };

  const handleTierChange = (index: number, field: "quantity" | "price", value: string) => {
    const newTiers = [...targetTiers];
    newTiers[index][field] = value;
    setTargetTiers(newTiers);
  };

  const handleAddProviderTier = () => {
    setProviderTiers([...providerTiers, { minParticipants: "", pricePerUnit: "", ourCommission: "", finalPrice: "" }]);
  };

  const handleRemoveProviderTier = (index: number) => {
    const newTiers = providerTiers.filter((_, i) => i !== index);
    setProviderTiers(newTiers);
  };

  const handleProviderTierChange = (index: number, field: keyof typeof providerTiers[0], value: string) => {
    const newTiers = [...providerTiers];
    newTiers[index][field] = value;
    setProviderTiers(newTiers);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    if (!productId) {
      setError("Please select a product.");
      setIsLoading(false);
      return;
    }

    try {
      await createGroupBuy({
        productId,
        targetTiers: targetTiers.map(t => ({ quantity: Number(t.quantity), price: Number(t.price) })),
        endTime: new Date(endTime).getTime(),
        providerPricingTiers: providerTiers.map(t => ({
          minParticipants: Number(t.minParticipants),
          pricePerUnit: Number(t.pricePerUnit),
          ourCommission: Number(t.ourCommission),
          finalPrice: Number(t.finalPrice),
        })),
      });
      router.push("/dashboard/group-buys");
    } catch (err: unknown) {
      setError(err instanceof Error ? err.message : "An error occurred.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Campaign Details</CardTitle>
          <CardDescription>Select a product and set the campaign duration.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="product">Product</Label>
            <Select onValueChange={(value) => setProductId(value as Id<"products">)} value={productId}>
              <SelectTrigger id="product">
                <SelectValue placeholder="Select a product" />
              </SelectTrigger>
              <SelectContent>
                {(Array.isArray(products) ? products : products?.page || []).map((product: Doc<"products">) => (
                  <SelectItem key={product._id} value={product._id}>
                    {product.title}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="endTime">End Time</Label>
            <Input 
              id="endTime" 
              type="datetime-local" 
              value={endTime} 
              onChange={(e) => setEndTime(e.target.value)} 
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Target Tiers</CardTitle>
          <CardDescription>Define the pricing tiers based on the number of participants.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {targetTiers.map((tier, index) => (
            <div key={index} className="flex items-center gap-4">
              <Input 
                type="number" 
                placeholder="Quantity" 
                value={tier.quantity} 
                onChange={(e) => handleTierChange(index, "quantity", e.target.value)} 
              />
              <Input 
                type="number" 
                placeholder="Price" 
                value={tier.price} 
                onChange={(e) => handleTierChange(index, "price", e.target.value)} 
              />
              <Button type="button" variant="outline" size="icon" onClick={() => handleRemoveTier(index)}>
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          ))}
          <Button type="button" variant="outline" onClick={handleAddTier}>
            <Plus className="h-4 w-4 mr-2" />
            Add Tier
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Provider Pricing Tiers</CardTitle>
          <CardDescription>Define the pricing tiers from the provider.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {providerTiers.map((tier, index) => (
            <div key={index} className="grid grid-cols-5 gap-4 items-center">
              <Input type="number" placeholder="Min Participants" value={tier.minParticipants} onChange={(e) => handleProviderTierChange(index, "minParticipants", e.target.value)} />
              <Input type="number" placeholder="Price Per Unit" value={tier.pricePerUnit} onChange={(e) => handleProviderTierChange(index, "pricePerUnit", e.target.value)} />
              <Input type="number" placeholder="Our Commission" value={tier.ourCommission} onChange={(e) => handleProviderTierChange(index, "ourCommission", e.target.value)} />
              <Input type="number" placeholder="Final Price" value={tier.finalPrice} onChange={(e) => handleProviderTierChange(index, "finalPrice", e.target.value)} />
              <Button type="button" variant="outline" size="icon" onClick={() => handleRemoveProviderTier(index)}>
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          ))}
          <Button type="button" variant="outline" onClick={handleAddProviderTier}>
            <Plus className="h-4 w-4 mr-2" />
            Add Provider Tier
          </Button>
        </CardContent>
      </Card>

      {error && <p className="text-red-500">{error}</p>}

      <div className="flex justify-end gap-4">
        <Button type="button" variant="outline" onClick={() => router.back()}>
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? "Creating..." : "Create Group Buy"}
        </Button>
      </div>
    </form>
  );
}
