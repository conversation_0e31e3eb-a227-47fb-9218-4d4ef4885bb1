import React from "react";
import { ManagementTable } from "./ManagementTable";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Doc } from "@/convex/_generated/dataModel";

interface GroupBuyWithProduct extends Doc<"groupBuys"> {
  product?: {
    title?: string;
  } | null;
  progress?: number;
}

interface GroupBuysManagementProps {
  groupBuys: GroupBuyWithProduct[];
}

export function GroupBuysManagement({ groupBuys }: GroupBuysManagementProps) {
  if (!groupBuys) {
    return <div>Loading...</div>;
  }

  const columns = [
    {
      header: "Product",
      accessor: (groupBuy: GroupBuyWithProduct) => groupBuy.product?.title ?? "N/A",
    },
    {
      header: "Status",
      accessor: (groupBuy: Doc<"groupBuys">) => <Badge>{groupBuy.status}</Badge>,
    },
    {
      header: "Participants",
      accessor: (groupBuy: Doc<"groupBuys">) => groupBuy.currentParticipants,
    },
    {
      header: "Progress",
      accessor: (groupBuy: GroupBuyWithProduct) => (
        <div className="flex items-center">
          <Progress value={groupBuy.progress || 0} className="w-2/3" />
          <span className="ml-2 text-sm text-muted-foreground">{Math.round(groupBuy.progress || 0)}%</span>
        </div>
      ),
    },
    {
      header: "End Time",
      accessor: (groupBuy: Doc<"groupBuys">) => new Date(groupBuy.endTime).toLocaleString(),
    },
  ];

  const handleViewDetails = (groupBuy: Doc<"groupBuys">) => {
    // TODO: Implement view details functionality
    console.log("View details for:", groupBuy);
  };

  return (
    <ManagementTable
      data={groupBuys}
      columns={columns}
      onViewDetails={handleViewDetails}
    />
  );
}