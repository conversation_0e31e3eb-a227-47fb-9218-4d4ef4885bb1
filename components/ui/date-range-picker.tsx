import * as React from "react";
import { CalendarIcon, X } from "lucide-react";
import { format } from "date-fns";
import { DateRange } from "react-day-picker";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface DatePickerWithRangeProps {
  dateRange: DateRange | undefined;
  onDateRangeChange: (range: DateRange | undefined) => void;
  placeholder?: string;
  className?: string;
}

export function DatePickerWithRange({
  dateRange,
  onDateRangeChange,
  placeholder = "Pick a date range",
  className,
}: DatePickerWithRangeProps) {
  const [open, setOpen] = React.useState(false);
  const [tempRange, setTempRange] = React.useState<DateRange | undefined>(dateRange);

  React.useEffect(() => {
    if (open) {
      setTempRange(dateRange);
    }
  }, [open, dateRange]);

  const handleClear = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDateRangeChange(undefined);
  };

  return (
    <div className={cn("grid gap-2", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            id="date"
            variant="outline"
            className={cn(
              "w-full justify-between text-left font-normal",
              !dateRange && "text-muted-foreground"
            )}
          >
            <div className="flex items-center gap-2">
              <CalendarIcon className="mr-2 h-4 w-4" />
              {dateRange?.from ? (
                dateRange.to ? (
                  <>
                    {format(dateRange.from, "LLL dd, y")} -{" "}
                    {format(dateRange.to, "LLL dd, y")}
                  </>
                ) : (
                  format(dateRange.from, "LLL dd, y")
                )
              ) : (
                <span>{placeholder}</span>
              )}
            </div>
            {dateRange && (
              <Button
                variant="ghost"
                size="sm"
                className="h-auto p-1 ml-2"
                onClick={handleClear}
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            initialFocus
            mode="range"
            defaultMonth={tempRange?.from}
            selected={tempRange}
            onSelect={(range) => {
              setTempRange(range);
            }}
            numberOfMonths={2}
            className="rounded-md border"
          />
          <div className="p-3 border-t">
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => {
                  setTempRange(undefined);
                }}
                className="flex-1"
              >
                Clear
              </Button>
              <Button
                onClick={() => {
                  onDateRangeChange(tempRange);
                  setOpen(false);
                }}
                className="flex-1"
              >
                Apply
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
}

export default DatePickerWithRange;